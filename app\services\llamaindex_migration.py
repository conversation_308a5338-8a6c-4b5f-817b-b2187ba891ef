"""
LlamaIndex Migration Service
Handles migration from LangChain-based vector database to LlamaIndex-based system.
"""

import os
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain.schema import Document as LangChainDocument
from llama_index.core import Document as LlamaIndexDocument

from app.services.vector_db import get_vector_db, similarity_search_with_category_filter
from app.services.llamaindex_vector_db import get_llamaindex_vector_db
from app.services.vector_db import get_vector_db, similarity_search_with_category_filter
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class LlamaIndexMigrationService:
    """
    Service to handle migration from LangChain-based to LlamaIndex-based vector database.
    """
    
    def __init__(self):
        self.llamaindex_db = get_llamaindex_vector_db()
        self.migration_log = []
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def migrate_category(self, category: str, batch_size: int = 100) -> Dict[str, Any]:
        """
        Migrate documents from a specific category to LlamaIndex.
        
        Args:
            category: Category to migrate
            batch_size: Number of documents to process in each batch
            
        Returns:
            Migration results
        """
        try:
            logger.info(f"Starting migration for category: {category}")
            
            # Get documents from existing system
            existing_docs = self._get_existing_documents(category)
            
            if not existing_docs:
                logger.info(f"No documents found for category: {category}")
                return {
                    "category": category,
                    "status": "completed",
                    "migrated_count": 0,
                    "errors": []
                }
            
            # Convert to LlamaIndex documents
            llamaindex_docs = self._convert_to_llamaindex_documents(existing_docs)
            
            # Migrate in batches
            migrated_count = 0
            errors = []
            
            for i in range(0, len(llamaindex_docs), batch_size):
                batch = llamaindex_docs[i:i + batch_size]
                try:
                    self.llamaindex_db.add_documents(batch, category)
                    migrated_count += len(batch)
                    logger.info(f"Migrated batch {i//batch_size + 1} for category {category}: {len(batch)} documents")
                except Exception as e:
                    error_msg = f"Failed to migrate batch {i//batch_size + 1}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # Log migration
            migration_entry = {
                "category": category,
                "timestamp": datetime.now().isoformat(),
                "migrated_count": migrated_count,
                "total_documents": len(existing_docs),
                "errors": errors
            }
            self.migration_log.append(migration_entry)
            
            logger.info(f"Migration completed for category {category}: {migrated_count}/{len(existing_docs)} documents")
            
            return {
                "category": category,
                "status": "completed",
                "migrated_count": migrated_count,
                "total_documents": len(existing_docs),
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Migration failed for category {category}: {str(e)}")
            return {
                "category": category,
                "status": "failed",
                "error": str(e),
                "migrated_count": 0
            }
    
    def _get_existing_documents(self, category: str) -> List[LangChainDocument]:
        """Get existing documents from the current system."""
        try:
            # Try unified vector database first
            try:
                vector_db = get_vector_db(category)
                # Get all documents for the category
                # This is a simplified approach - in practice, you'd need to implement
                # a method to retrieve all documents from the unified database
                logger.info(f"Retrieved documents from unified database for category: {category}")
                return []  # Placeholder - implement actual retrieval
            except Exception as e:
                logger.warning(f"Failed to get documents from unified database: {str(e)}")
            
            # Fallback to old vector database
            try:
                vector_db = get_vector_db(category)
                # This would need to be implemented based on the actual vector_db interface
                logger.info(f"Retrieved documents from vector database for category: {category}")
                return []  # Placeholder - implement actual retrieval
            except Exception as e:
                logger.warning(f"Failed to get documents from vector database: {str(e)}")
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to get existing documents: {str(e)}")
            return []
    
    def _convert_to_llamaindex_documents(self, langchain_docs: List[LangChainDocument]) -> List[LlamaIndexDocument]:
        """Convert LangChain documents to LlamaIndex documents."""
        try:
            llamaindex_docs = []
            
            for doc in langchain_docs:
                # Convert LangChain document to LlamaIndex document
                llamaindex_doc = LlamaIndexDocument(
                    text=doc.page_content,
                    metadata=doc.metadata.copy() if hasattr(doc, 'metadata') and doc.metadata else {},
                    id_=doc.metadata.get('id', None) if hasattr(doc, 'metadata') and doc.metadata else None
                )
                llamaindex_docs.append(llamaindex_doc)
            
            logger.info(f"Converted {len(llamaindex_docs)} documents to LlamaIndex format")
            return llamaindex_docs
            
        except Exception as e:
            logger.error(f"Failed to convert documents: {str(e)}")
            return []
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def migrate_all_categories(self, categories: List[str] = None, batch_size: int = 100) -> Dict[str, Any]:
        """
        Migrate all categories to LlamaIndex.
        
        Args:
            categories: List of categories to migrate (None for all)
            batch_size: Number of documents to process in each batch
            
        Returns:
            Overall migration results
        """
        try:
            if categories is None:
                # Get all categories from the system
                categories = self._get_all_categories()
            
            logger.info(f"Starting migration for {len(categories)} categories")
            
            results = {}
            total_migrated = 0
            total_documents = 0
            all_errors = []
            
            for category in categories:
                result = self.migrate_category(category, batch_size)
                results[category] = result
                
                if result["status"] == "completed":
                    total_migrated += result.get("migrated_count", 0)
                    total_documents += result.get("total_documents", 0)
                    all_errors.extend(result.get("errors", []))
                else:
                    all_errors.append(f"Category {category}: {result.get('error', 'Unknown error')}")
            
            # Save migration log
            self._save_migration_log()
            
            overall_result = {
                "status": "completed",
                "categories_processed": len(categories),
                "total_migrated": total_migrated,
                "total_documents": total_documents,
                "success_rate": (total_migrated / total_documents * 100) if total_documents > 0 else 0,
                "errors": all_errors,
                "category_results": results
            }
            
            logger.info(f"Migration completed: {total_migrated}/{total_documents} documents migrated")
            return overall_result
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    def _get_all_categories(self) -> List[str]:
        """Get all categories from the current system."""
        try:
            # This would need to be implemented based on the actual system
            # For now, return common categories
            return ["CANOPY", "CANOPY2", "MANUAL", "RISE", "DEMO"]
        except Exception as e:
            logger.error(f"Failed to get categories: {str(e)}")
            return []
    
    def _save_migration_log(self):
        """Save migration log to file."""
        try:
            log_file = os.path.join(
                os.getenv("LOGS_DIR", "./logs"),
                f"llamaindex_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            with open(log_file, 'w') as f:
                json.dump(self.migration_log, f, indent=2)
            
            logger.info(f"Migration log saved to: {log_file}")
            
        except Exception as e:
            logger.error(f"Failed to save migration log: {str(e)}")
    
    def verify_migration(self, category: str) -> Dict[str, Any]:
        """
        Verify that migration was successful by comparing results.
        
        Args:
            category: Category to verify
            
        Returns:
            Verification results
        """
        try:
            # Get document count from old system
            old_count = self._get_document_count_old(category)
            
            # Get document count from new system
            new_count = self._get_document_count_new(category)
            
            # Perform test queries
            test_queries = [
                "test query 1",
                "test query 2",
                "test query 3"
            ]
            
            query_results = {}
            for query in test_queries:
                try:
                    # Test old system
                    old_results = self._test_query_old(query, category)
                    
                    # Test new system
                    new_results = self._test_query_new(query, category)
                    
                    query_results[query] = {
                        "old_results_count": len(old_results),
                        "new_results_count": len(new_results),
                        "results_match": len(old_results) == len(new_results)
                    }
                except Exception as e:
                    query_results[query] = {"error": str(e)}
            
            verification_result = {
                "category": category,
                "old_document_count": old_count,
                "new_document_count": new_count,
                "count_match": old_count == new_count,
                "query_results": query_results,
                "migration_successful": old_count == new_count and all(
                    result.get("results_match", False) for result in query_results.values()
                    if "error" not in result
                )
            }
            
            logger.info(f"Verification completed for category {category}: {verification_result['migration_successful']}")
            return verification_result
            
        except Exception as e:
            logger.error(f"Verification failed for category {category}: {str(e)}")
            return {
                "category": category,
                "error": str(e),
                "migration_successful": False
            }
    
    def _get_document_count_old(self, category: str) -> int:
        """Get document count from old system."""
        try:
            # Implement based on actual system
            return 0  # Placeholder
        except Exception as e:
            logger.error(f"Failed to get old document count: {str(e)}")
            return 0
    
    def _get_document_count_new(self, category: str) -> int:
        """Get document count from new system."""
        try:
            stats = self.llamaindex_db.get_collection_stats()
            return stats.get("total_documents", 0)
        except Exception as e:
            logger.error(f"Failed to get new document count: {str(e)}")
            return 0
    
    def _test_query_old(self, query: str, category: str) -> List:
        """Test query on old system."""
        try:
            # Implement based on actual system
            return []  # Placeholder
        except Exception as e:
            logger.error(f"Failed to test query on old system: {str(e)}")
            return []
    
    def _test_query_new(self, query: str, category: str) -> List:
        """Test query on new system."""
        try:
            results = self.llamaindex_db.similarity_search(query, category, k=5)
            return results
        except Exception as e:
            logger.error(f"Failed to test query on new system: {str(e)}")
            return []


# Global migration service instance
_migration_service = None


def get_migration_service() -> LlamaIndexMigrationService:
    """Get the global migration service instance."""
    global _migration_service
    if _migration_service is None:
        _migration_service = LlamaIndexMigrationService()
    return _migration_service


def migrate_to_llamaindex(categories: List[str] = None, batch_size: int = 100) -> Dict[str, Any]:
    """
    Convenience function to migrate to LlamaIndex.
    
    Args:
        categories: List of categories to migrate (None for all)
        batch_size: Number of documents to process in each batch
        
    Returns:
        Migration results
    """
    migration_service = get_migration_service()
    return migration_service.migrate_all_categories(categories, batch_size)


def verify_llamaindex_migration(category: str) -> Dict[str, Any]:
    """
    Convenience function to verify LlamaIndex migration.
    
    Args:
        category: Category to verify
        
    Returns:
        Verification results
    """
    migration_service = get_migration_service()
    return migration_service.verify_migration(category) 