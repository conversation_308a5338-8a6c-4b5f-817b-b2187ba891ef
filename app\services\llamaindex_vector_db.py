"""
LlamaIndex Vector Database Service
Provides a unified LlamaIndex-based vector database with enhanced retrieval capabilities.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from llama_index.core import VectorStoreIndex, Document, Settings, StorageContext
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.embeddings.ollama import OllamaEmbedding
from llama_index.core.node_parser import Sen<PERSON>ce<PERSON>plitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.indices.query.embedding_utils import get_top_k_embeddings

import chromadb
from chromadb.config import Settings as ChromaSettings

from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class LlamaIndexVectorDB:
    """
    LlamaIndex-based vector database service that provides enhanced retrieval capabilities
    with hybrid scoring and advanced query processing.
    """
    
    def __init__(self, collection_name: str = "llamaindex_collection"):
        self.persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        self.collection_name = collection_name
        self.embedding_model = os.getenv("TEXT_EMBEDDING_MODEL", "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q6_K")
        
        # LlamaIndex components
        self._embed_model = None
        self._chroma_client = None
        self._vector_store = None
        self._index = None
        self._storage_context = None
        
        # Ensure directory exists
        os.makedirs(self.persist_directory, exist_ok=True)
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize LlamaIndex components."""
        try:
            # Initialize embedding model
            self._embed_model = OllamaEmbedding(model_name=self.embedding_model)
            logger.info(f"Initialized LlamaIndex embedding model: {self.embedding_model}")

            # Initialize ChromaDB client with consistent settings to avoid conflicts
            self._chroma_client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=False  # Changed to False to match vector_db.py settings
                )
            )
            
            # Get or create collection
            try:
                self._chroma_collection = self._chroma_client.get_collection(self.collection_name)
                logger.info(f"Using existing ChromaDB collection: {self.collection_name}")
            except:
                self._chroma_collection = self._chroma_client.create_collection(self.collection_name)
                logger.info(f"Created new ChromaDB collection: {self.collection_name}")
            
            # Initialize vector store
            self._vector_store = ChromaVectorStore(
                chroma_collection=self._chroma_collection
            )
            
            # Initialize storage context
            self._storage_context = StorageContext.from_defaults(
                vector_store=self._vector_store
            )
            
            # Initialize index
            self._index = VectorStoreIndex.from_vector_store(
                vector_store=self._vector_store,
                storage_context=self._storage_context,
                embed_model=self._embed_model
            )
            
            logger.info("LlamaIndex vector database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize LlamaIndex components: {str(e)}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def add_documents(self, documents: List[Document], category: str, **kwargs):
        """
        Add documents to the LlamaIndex vector database with category metadata.
        
        Args:
            documents: List of LlamaIndex documents to add
            category: Category for the documents
            **kwargs: Additional metadata
        """
        try:
            # Prepare documents with category metadata
            processed_docs = []
            for doc in documents:
                # Ensure document has metadata
                if not hasattr(doc, 'metadata') or doc.metadata is None:
                    doc.metadata = {}
                
                # Add category and additional metadata
                doc.metadata['category'] = category
                doc.metadata.update(kwargs)
                processed_docs.append(doc)
            
            # Add documents to index
            self._index.insert_nodes(processed_docs)
            logger.info(f"Added {len(documents)} documents to category: {category}")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {str(e)}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def similarity_search(self, query: str, category: Optional[str] = None, 
                         k: int = 10, **kwargs) -> List[Document]:
        """
        Perform similarity search with optional category filtering.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            # Create retriever
            retriever = VectorIndexRetriever(
                index=self._index,
                similarity_top_k=k * 2 if category else k  # Get more results if we need to filter
            )
            
            # Perform retrieval
            query_bundle = QueryBundle(query_str=query)
            nodes = retriever.retrieve(query_bundle)
            
            # Filter by category if specified
            if category:
                nodes = [node for node in nodes if node.metadata.get('category') == category]
                nodes = nodes[:k]  # Limit to requested k
            
            # Convert nodes to documents
            documents = []
            for node in nodes:
                doc = Document(
                    text=node.text,
                    metadata=node.metadata,
                    id_=node.node_id
                )
                documents.append(doc)
            
            logger.info(f"Retrieved {len(documents)} documents for query: {query[:50]}...")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {str(e)}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def similarity_search_with_score(self, query: str, category: Optional[str] = None,
                                   k: int = 10, **kwargs) -> List[tuple]:
        """
        Perform similarity search with scores.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of (document, score) tuples
        """
        try:
            # Create retriever
            retriever = VectorIndexRetriever(
                index=self._index,
                similarity_top_k=k * 2 if category else k  # Get more results if we need to filter
            )
            
            # Perform retrieval
            query_bundle = QueryBundle(query_str=query)
            nodes = retriever.retrieve(query_bundle)
            
            # Filter by category if specified
            if category:
                nodes = [node for node in nodes if node.metadata.get('category') == category]
                nodes = nodes[:k]  # Limit to requested k
            
            # Convert nodes to documents with scores
            results = []
            for node in nodes:
                doc = Document(
                    text=node.text,
                    metadata=node.metadata,
                    id_=node.node_id
                )
                score = getattr(node, 'score', 0.0)
                results.append((doc, score))
            
            logger.info(f"Retrieved {len(results)} documents with scores for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search with scores: {str(e)}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def hybrid_search(self, query: str, category: Optional[str] = None,
                     k: int = 10, alpha: float = 0.5, **kwargs) -> List[tuple]:
        """
        Perform hybrid search combining vector similarity and document-level scoring.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            alpha: Weight for hybrid scoring (0.0 = document-level only, 1.0 = vector only)
            **kwargs: Additional search parameters
            
        Returns:
            List of (document, score) tuples
        """
        try:
            # Get vector similarity results
            vector_results = self.similarity_search_with_score(query, category, k * 2, **kwargs)
            
            if not vector_results:
                return []
            
            # Get query embedding for document-level scoring
            query_embedding = self._embed_model.get_query_embedding(query)
            
            # Compute document-level similarities
            doc_embeddings = []
            for doc, _ in vector_results:
                # Get document embedding (this would need to be stored or computed)
                # For now, we'll use a simplified approach
                doc_embeddings.append(query_embedding)  # Placeholder
            
            # Combine scores
            hybrid_results = []
            for i, (doc, vector_score) in enumerate(vector_results):
                if i < len(doc_embeddings):
                    doc_score = 1.0  # Placeholder - would be actual document similarity
                    hybrid_score = (alpha * vector_score) + ((1 - alpha) * doc_score)
                    hybrid_results.append((doc, hybrid_score))
                else:
                    hybrid_results.append((doc, vector_score))
            
            # Sort by hybrid score and return top k
            hybrid_results.sort(key=lambda x: x[1], reverse=True)
            results = hybrid_results[:k]
            
            logger.info(f"Performed hybrid search with {len(results)} results for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform hybrid search: {str(e)}")
            # Fallback to vector search
            return self.similarity_search_with_score(query, category, k, **kwargs)
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def query_documents(self, query: str, category: Optional[str] = None,
                       response_mode: str = "tree_summarize", **kwargs) -> Dict[str, Any]:
        """
        Query documents using LlamaIndex query engine.
        
        Args:
            query: Query string
            category: Optional category filter
            response_mode: Response generation mode
            **kwargs: Additional query parameters
            
        Returns:
            Dictionary with response and metadata
        """
        try:
            # Create retriever
            retriever = VectorIndexRetriever(
                index=self._index,
                similarity_top_k=kwargs.get('k', 5)
            )
            
            # Filter by category if specified
            if category:
                # Get all nodes and filter by category
                all_nodes = retriever.retrieve(QueryBundle(query_str=query))
                filtered_nodes = [node for node in all_nodes if node.metadata.get('category') == category]
                filtered_nodes = filtered_nodes[:kwargs.get('k', 5)]
            else:
                filtered_nodes = retriever.retrieve(QueryBundle(query_str=query))
            
            # Create a simple response without using query engine (to avoid LLM dependency)
            result = {
                "response": f"Found {len(filtered_nodes)} relevant documents for your query: '{query}'",
                "source_nodes": [
                    {
                        "text": node.text,
                        "metadata": node.metadata,
                        "score": getattr(node, 'score', None)
                    }
                    for node in filtered_nodes
                ],
                "total_sources": len(filtered_nodes)
            }
            
            logger.info(f"Query completed with {len(filtered_nodes)} sources")
            return result
            
        except Exception as e:
            logger.error(f"Failed to query documents: {str(e)}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            stats = {
                "collection_name": self.collection_name,
                "persist_directory": self.persist_directory,
                "embedding_model": self.embedding_model,
                "total_documents": self._chroma_collection.count()
            }
            return stats
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            return {}
    
    def delete_documents(self, category: Optional[str] = None, 
                        filter_dict: Optional[Dict] = None):
        """Delete documents from the collection."""
        try:
            # Build filter
            filters = {}
            if category:
                filters["category"] = category
            if filter_dict:
                filters.update(filter_dict)
            
            if filters:
                # Delete by filter
                self._chroma_collection.delete(where=filters)
                logger.info(f"Deleted documents with filters: {filters}")
            else:
                # Delete all documents
                self._chroma_collection.delete()
                logger.info("Deleted all documents from collection")
                
        except Exception as e:
            logger.error(f"Failed to delete documents: {str(e)}")
            raise
    
    def optimize_collection(self):
        """Optimize the collection for better performance."""
        try:
            # ChromaDB optimization
            self._chroma_collection.persist()
            logger.info("Collection optimized and persisted")
        except Exception as e:
            logger.error(f"Failed to optimize collection: {str(e)}")
            raise


# Global instance
_llamaindex_vector_db = None


def get_llamaindex_vector_db() -> LlamaIndexVectorDB:
    """Get the global LlamaIndex vector database instance."""
    global _llamaindex_vector_db
    if _llamaindex_vector_db is None:
        try:
            _llamaindex_vector_db = LlamaIndexVectorDB()
        except Exception as e:
            logger.error(f"Failed to initialize LlamaIndex vector database: {str(e)}")
            logger.warning("LlamaIndex vector database is temporarily disabled due to ChromaDB conflict")
            # Return a mock object that raises NotImplementedError for all methods
            class MockLlamaIndexVectorDB:
                def __getattr__(self, name):
                    def method(*args, **kwargs):
                        raise NotImplementedError("LlamaIndex vector database is temporarily disabled")
                    return method
            _llamaindex_vector_db = MockLlamaIndexVectorDB()
    return _llamaindex_vector_db


def get_llamaindex_vector_db_by_category(category: str) -> LlamaIndexVectorDB:
    """Get LlamaIndex vector database instance for a specific category."""
    # For now, use the same instance with category filtering
    # In the future, we could create separate collections per category
    return get_llamaindex_vector_db() 