import os
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
import logging
import requests
from chromadb.config import Settings
import json

# Import ChromaDB performance monitoring
from app.utils.chroma_performance import (
    monitor_chroma_operation,
    monitor_similarity_search,
    monitor_add_documents,
    get_chroma_monitor
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Update to use unified database path
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q6_K")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]

def check_embedding_model_availability(model_name):
    """Check if the specified embedding model is available in Ollama."""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning(f"Ollama service returned status code: {response.status_code}")
            return False

        # Check if the embedding model is available
        models = response.json().get("models", [])
        model_names = [model.get("name") for model in models]

        if model_name in model_names:
            logger.info(f"Embedding model '{model_name}' is available")
            return True
        else:
            logger.warning(f"Embedding model '{model_name}' not found in available models: {', '.join(model_names)}")
            return False
    except Exception as e:
        logger.warning(f"Failed to check embedding model availability: {str(e)}")
        return False

def _get_embedding_prompts():
    try:
        with open(os.path.join(os.path.dirname(__file__), '../../config/default_models.json'), 'r') as f:
            config = json.load(f)
        params = config.get('embedding_parameters', {})
        return params.get('embedding_prompt', ''), params.get('query_prompt', '')
    except Exception as e:
        logger.warning(f"Could not load embedding prompts from config: {e}")
        return '', ''

# Remove PromptedOllamaEmbeddings and always use OllamaEmbeddings batching for all models

# Global ChromaDB instance (singleton)
_global_chroma_instance = None
_chroma_client = None

def get_vector_db(category: str) -> Chroma:
    """
    Get the global Chroma vector database instance.
    Uses unified database with metadata filtering for all categories.

    Args:
        category (str): The category name (used for metadata filtering).

    Returns:
        Chroma: The Chroma vector database instance.
    """
    global _global_chroma_instance, _chroma_client
    
    try:
        # Return global instance if available
        if _global_chroma_instance is not None:
            logger.debug(f"Reusing global Chroma DB instance for category: {category}")
            return _global_chroma_instance
        
        # Clear any existing instances first to prevent conflicts
        clear_chroma_cache()

        # Try to initialize with the configured embedding model
        model_to_use = TEXT_EMBEDDING_MODEL

        # Check if the model is available
        if not check_embedding_model_availability(model_to_use):
            # Try fallback models if the primary one isn't available
            for fallback_model in FALLBACK_EMBEDDING_MODELS:
                if fallback_model != model_to_use and check_embedding_model_availability(fallback_model):
                    logger.info(f"Using fallback embedding model: {fallback_model}")
                    model_to_use = fallback_model
                    break

        logger.info(f"Initializing embedding function with model: {model_to_use}")
        try:
            embedding_prompt, query_prompt = _get_embedding_prompts()
            embed_fn = OllamaEmbeddings(model=model_to_use)
            logger.info(f"Successfully initialized embedding function with model: {model_to_use}")
        except Exception as embed_error:
            logger.error(f"Failed to initialize embedding function with model {model_to_use}: {str(embed_error)}")
            # Last resort fallback to nomic-embed-text
            logger.info("Trying last resort fallback to nomic-embed-text:latest")
            try:
                embed_fn = OllamaEmbeddings(model="nomic-embed-text:latest")
                logger.info("Successfully initialized fallback embedding function with nomic-embed-text:latest")
            except Exception as fallback_error:
                logger.error(f"Failed to initialize fallback embedding model: {str(fallback_error)}")
                raise ValueError(f"Could not initialize any embedding model. Original error: {str(embed_error)}, Fallback error: {str(fallback_error)}")

        # Create the unified vector database
        persist_dir = CHROMA_PATH
        os.makedirs(persist_dir, exist_ok=True)
        logger.info(f"Initializing unified Chroma vector DB at {persist_dir}")

        try:
            # Disable telemetry and configure settings to prevent conflicts
            telemetry_settings = Settings(
                anonymized_telemetry=False,
                allow_reset=False  # Prevent conflicts with other ChromaDB instances
            )
            
            # First, try to get or create the ChromaDB client
            if _chroma_client is None:
                try:
                    import chromadb
                    _chroma_client = chromadb.PersistentClient(
                        path=persist_dir,
                        settings=telemetry_settings
                    )
                    logger.info(f"Created new ChromaDB client for {persist_dir}")
                except Exception as client_error:
                    logger.warning(f"Failed to create ChromaDB client: {str(client_error)}")
                    # Fall back to direct Chroma initialization
                    _chroma_client = None
            
            # Use unified collection name
            if _chroma_client is not None:
                # Try to use the client approach first
                try:
                    # Get or create collection
                    try:
                        collection = _chroma_client.get_collection("unified_collection")
                        logger.info("Using existing ChromaDB collection")
                    except:
                        collection = _chroma_client.create_collection("unified_collection")
                        logger.info("Created new ChromaDB collection")
                    
                    # Create Chroma instance with the collection
                    db = Chroma(
                        collection_name="unified_collection",
                        persist_directory=persist_dir,
                        embedding_function=embed_fn,
                        client_settings=telemetry_settings
                    )
                except Exception as client_db_error:
                    logger.warning(f"Client approach failed, falling back to direct initialization: {str(client_db_error)}")
                    # Fall back to direct initialization
                    db = Chroma(
                        collection_name="unified_collection",
                        persist_directory=persist_dir,
                        embedding_function=embed_fn,
                        client_settings=telemetry_settings
                    )
            else:
                # Direct initialization
                db = Chroma(
                    collection_name="unified_collection",
                    persist_directory=persist_dir,
                    embedding_function=embed_fn,
                    client_settings=telemetry_settings
                )

            # Store the global instance
            _global_chroma_instance = db
            logger.info("Successfully created global ChromaDB instance")
            return db

        except ValueError as ve:
            # Handle dimension mismatch error specifically
            error_msg = str(ve).lower()
            if "embedding dimension" in error_msg and "does not match collection dimensionality" in error_msg:
                # Extract dimensions from error message
                import re
                match = re.search(r'embedding dimension (\d+) does not match collection dimensionality (\d+)', error_msg)
                if match:
                    current_dim = match.group(1)
                    collection_dim = match.group(2)

                    detailed_error = (
                        f"Dimension mismatch error: Your current embedding model ({model_to_use}) "
                        f"produces {current_dim}-dimensional vectors, but the existing collection "
                        f"was created with {collection_dim}-dimensional vectors.\n\n"
                        f"To fix this issue, you have two options:\n"
                        f"1. Run 'python switch_embedding_model.py --auto' to automatically switch to a compatible model\n"
                        f"2. Run 'python reembed_documents.py' to re-embed all documents with your current model\n\n"
                        f"See the documentation for more details on these options."
                    )
                    logger.error(detailed_error)
                    raise ValueError(detailed_error) from ve
            
            # Handle ChromaDB instance conflict error
            elif "an instance of chroma already exists" in error_msg:
                logger.warning("ChromaDB instance conflict detected. Attempting to clear and recreate...")
                try:
                    # Clear the global instance and try again
                    clear_chroma_cache()
                    
                    # Wait a moment for cleanup
                    import time
                    time.sleep(1)
                    
                    # Try to create a new instance
                    db = Chroma(
                        collection_name="unified_collection",
                        persist_directory=persist_dir,
                        embedding_function=embed_fn,
                        client_settings=telemetry_settings
                    )
                    
                    _global_chroma_instance = db
                    logger.info("Successfully recreated ChromaDB instance after conflict resolution")
                    return db
                    
                except Exception as retry_error:
                    logger.error(f"Failed to recreate ChromaDB instance: {str(retry_error)}")
                    raise ValueError(f"ChromaDB instance conflict could not be resolved. Please restart the application. Original error: {str(ve)}") from ve

            # Re-raise the original error if it's not a handled case
            raise

    except Exception as e:
        logger.error(f"Failed to initialize vector DB for category {category}: {str(e)}")

        # Provide more detailed error information for common issues
        error_msg = str(e).lower()
        if "str object has no attribute get" in error_msg:
            logger.error("This appears to be an issue with model initialization. Check that Ollama is running and the embedding model is available.")
        elif "connection refused" in error_msg:
            logger.error("Could not connect to Ollama. Make sure the Ollama service is running.")
        elif "no such file or directory" in error_msg:
            logger.error(f"The directory for category {category} does not exist or is not accessible.")

        raise

@monitor_similarity_search
def similarity_search_with_category_filter(query: str, category: str, k: int = 10, **kwargs):
    """
    Perform similarity search with category filtering.
    
    Args:
        query: Search query
        category: Category to filter by
        k: Number of results to return
        **kwargs: Additional search parameters
        
    Returns:
        List of similar documents
    """
    try:
        db = get_vector_db(category)
        
        # Use metadata filtering for category separation
        filter_dict = {"category": category}
        
        results = db.similarity_search(
            query,
            k=k,
            filter=filter_dict,
            **kwargs
        )
        
        logger.info(f"Found {len(results)} documents for query in category {category}")
        return results
        
    except Exception as e:
        logger.error(f"Failed to perform similarity search: {str(e)}")
        raise

@monitor_add_documents
def add_documents_with_category(documents, category: str, **kwargs):
    """
    Add documents to the unified database with category metadata.
    
    Args:
        documents: List of documents to add
        category: Category for the documents
        **kwargs: Additional metadata
    """
    try:
        db = get_vector_db(category)
        
        # Prepare documents with category metadata
        enhanced_documents = []
        for doc in documents:
            # Create a copy of the document with enhanced metadata
            enhanced_metadata = doc.metadata.copy() if hasattr(doc, 'metadata') else {}
            enhanced_metadata['category'] = category
            enhanced_metadata.update(kwargs)
            
            # Create new document with enhanced metadata
            from langchain.schema import Document
            enhanced_doc = Document(
                page_content=doc.page_content,
                metadata=enhanced_metadata
            )
            enhanced_documents.append(enhanced_doc)
        
        # Add documents using the standard add_documents method
        db.add_documents(enhanced_documents)
        logger.info(f"Added {len(documents)} documents to category: {category}")
        
    except Exception as e:
        logger.error(f"Failed to add documents: {str(e)}")
        raise

def clear_chroma_cache():
    """Clear the global ChromaDB instance and client."""
    global _global_chroma_instance, _chroma_client

    # Close existing instances properly
    if _global_chroma_instance is not None:
        try:
            # Try to close the instance if it has a close method
            if hasattr(_global_chroma_instance, '_client') and hasattr(_global_chroma_instance._client, 'reset'):
                _global_chroma_instance._client.reset()
        except Exception as e:
            logger.debug(f"Error closing ChromaDB instance: {str(e)}")

    if _chroma_client is not None:
        try:
            # Try to close the client if it has a close method
            if hasattr(_chroma_client, 'reset'):
                _chroma_client.reset()
        except Exception as e:
            logger.debug(f"Error closing ChromaDB client: {str(e)}")

    _global_chroma_instance = None
    _chroma_client = None
    logger.info("Global ChromaDB instance and client cleared")