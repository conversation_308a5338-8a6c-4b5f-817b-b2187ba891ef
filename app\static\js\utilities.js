/**
 * Document Management System - Shared Utilities
 * Common JavaScript functions used across the application
 */

// Prevent multiple declarations of DMSUtils
(function() {
    'use strict';

    // Check if DMSUtils is already defined
    if (typeof window.DMSUtils !== 'undefined') {
        console.warn('DMSUtils already exists, skipping redefinition');
        return;
    }

    // Namespace for our utilities to avoid global scope pollution
    window.DMSUtils = {
    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showToast: function(message, type = 'success', duration = 3000) {
        // Check if Toastify is available
        if (typeof Toastify !== 'function') {
            console.error('Toastify library not loaded');
            alert(message);
            return;
        }

        // Set background color based on type
        let backgroundColor;
        switch (type) {
            case 'error':
                backgroundColor = '#dc3545'; // Bootstrap danger
                break;
            case 'warning':
                backgroundColor = '#ffc107'; // Bootstrap warning
                break;
            case 'info':
                backgroundColor = '#0dcaf0'; // Bootstrap info
                break;
            case 'success':
            default:
                backgroundColor = '#198754'; // Bootstrap success
                break;
        }

        Toastify({
            text: message,
            duration: duration,
            close: true,
            gravity: "top",
            position: "right",
            backgroundColor: backgroundColor,
            stopOnFocus: true
        }).showToast();
    },

    /**
     * Make an API request and handle common response patterns
     * @param {string} path - API endpoint path
     * @param {Object} opts - Fetch options
     * @returns {Promise} - Promise with response data
     */
    api: async function(path, opts = {}) {
        try {
            // Get CSRF token from meta tag
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // Add CSRF token to headers if not already present and if method requires it
            if (opts.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(opts.method.toUpperCase())) {
                opts.headers = opts.headers || {};
                if (!opts.headers['X-CSRFToken'] && csrfToken) {
                    opts.headers['X-CSRFToken'] = csrfToken;
                }
            }

            const res = await fetch(path, opts);
            let json;

            try {
                const text = await res.text();
                // Check if response is JSON
                if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                    json = JSON.parse(text);
                } else {
                    // If it's not JSON (e.g., HTML error page), create an error object
                    console.warn('Received non-JSON response:', text.substring(0, 100) + '...');
                    json = {
                        error: 'Server returned an unexpected response format',
                        details: text.includes('<!DOCTYPE') ? 'HTML error page received' : 'Non-JSON response'
                    };
                }
            } catch (e) {
                console.error('Failed to parse response:', e);
                json = {
                    error: 'Failed to parse server response',
                    details: e.message
                };
            }

            // Handle CSRF token expiration
            if (res.status === 400 && json.error && json.error.includes('CSRF')) {
                console.warn('CSRF token expired, attempting to refresh...');

                // Try to refresh the CSRF token
                const refreshed = await this.refreshCSRFToken();
                if (refreshed) {
                    // Retry the original request with the new token
                    const newCsrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                    if (opts.headers && newCsrfToken) {
                        opts.headers['X-CSRFToken'] = newCsrfToken;
                    }

                    // Retry the request
                    const retryRes = await fetch(path, opts);
                    let retryJson;
                    try {
                        const retryText = await retryRes.text();
                        if (retryText.trim().startsWith('{') || retryText.trim().startsWith('[')) {
                            retryJson = JSON.parse(retryText);
                        } else {
                            retryJson = {
                                error: 'Server returned an unexpected response format after retry',
                                details: retryText.includes('<!DOCTYPE') ? 'HTML error page received' : 'Non-JSON response'
                            };
                        }
                    } catch (e) {
                        retryJson = {
                            error: 'Failed to parse server response after retry',
                            details: e.message
                        };
                    }

                    return {
                        ok: retryRes.ok,
                        status: retryRes.status,
                        json: retryJson
                    };
                }
            }

            return {
                ok: res.ok,
                status: res.status,
                json
            };
        } catch (error) {
            console.error('API request failed:', error);
            return {
                ok: false,
                status: 0,
                json: { error: 'Network error. Please check your connection.' }
            };
        }
    },

    /**
     * Refresh the CSRF token
     * @returns {Promise<boolean>} - True if token was refreshed successfully
     */
    refreshCSRFToken: async function() {
        try {
            const response = await fetch('/api/csrf-token', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.csrf_token) {
                    // Update the meta tag with the new token
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.csrf_token);
                    }
                    
                    // Update all hidden input fields with csrf_token name
                    const csrfInputs = document.querySelectorAll('input[name="csrf_token"]');
                    csrfInputs.forEach(input => {
                        input.value = data.csrf_token;
                    });
                    
                    // Update any input with id="csrf-token"
                    const csrfTokenById = document.getElementById('csrf-token');
                    if (csrfTokenById) {
                        csrfTokenById.value = data.csrf_token;
                    }
                    
                    console.log('CSRF token refreshed successfully');
                    return true;
                } else {
                    console.error('No CSRF token in response');
                    return false;
                }
            } else {
                console.error('Failed to refresh CSRF token: HTTP', response.status);
                return false;
            }
        } catch (error) {
            console.error('Error refreshing CSRF token:', error);
            return false;
        }
    },

    /**
     * Confirm an action with the user
     * @param {string} message - Confirmation message
     * @param {Object} options - Additional options
     * @returns {Promise} - Resolves to boolean (true if confirmed)
     */
    confirm: function(message, options = {}) {
        const defaultOptions = {
            title: 'Confirm Action',
            confirmText: 'Confirm',
            cancelText: 'Cancel',
            dangerous: false
        };

        const settings = { ...defaultOptions, ...options };

        // If Bootstrap modal is available, use it for a nicer confirmation
        if (typeof bootstrap !== 'undefined' && typeof $ !== 'undefined') {
            return new Promise((resolve) => {
                // Create modal if it doesn't exist
                if (!document.getElementById('confirmationModal')) {
                    const modalHtml = `
                        <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="confirmationModalLabel">${settings.title}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        ${message}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${settings.cancelText}</button>
                                        <button type="button" class="btn ${settings.dangerous ? 'btn-danger' : 'btn-primary'}" id="confirmBtn">${settings.confirmText}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.insertAdjacentHTML('beforeend', modalHtml);

                    // Add event listener for the confirm button
                    document.getElementById('confirmBtn').addEventListener('click', function() {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmationModal'));
                        modal.hide();
                        resolve(true);
                    });

                    // Add event listener for modal hidden event
                    $('#confirmationModal').on('hidden.bs.modal', function() {
                        resolve(false);
                    });
                } else {
                    // Update existing modal
                    const modalLabel = document.getElementById('confirmationModalLabel');
                    const modalBody = document.querySelector('#confirmationModal .modal-body');
                    const cancelBtn = document.querySelector('#confirmationModal .btn-secondary');
                    const confirmBtn = document.getElementById('confirmBtn');
                    
                    if (modalLabel) modalLabel.textContent = settings.title;
                    if (modalBody) modalBody.textContent = message;
                    if (cancelBtn) cancelBtn.textContent = settings.cancelText;
                    if (confirmBtn) {
                        confirmBtn.textContent = settings.confirmText;
                        confirmBtn.className = `btn ${settings.dangerous ? 'btn-danger' : 'btn-primary'}`;
                    }

                    // Update event listener
                    const oldConfirmBtn = document.getElementById('confirmBtn');
                    let newConfirmBtn = null;
                    if (oldConfirmBtn && oldConfirmBtn.parentNode) {
                        newConfirmBtn = oldConfirmBtn.cloneNode(true);
                        oldConfirmBtn.parentNode.replaceChild(newConfirmBtn, oldConfirmBtn);
                    }
                    if (newConfirmBtn) {
                        newConfirmBtn.addEventListener('click', function() {
                            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmationModal'));
                            modal.hide();
                            resolve(true);
                        });
                    }
                }

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
                modal.show();
            });
        } else {
            // Fallback to browser confirm
            return Promise.resolve(confirm(message));
        }
    },

    /**
     * Toggle dark mode with enhanced functionality
     * @param {boolean} isDark - Whether to enable dark mode
     */
    toggleDarkMode: function(isDark) {
        if (isDark) {
            // Add dark mode classes for different templates
            document.documentElement.classList.add('dark-mode');
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light-mode');
            document.body.classList.add('dark-mode');
            document.body.classList.add('dark');
            document.body.classList.remove('light-mode');
            localStorage.setItem('theme', 'dark');
        } else {
            // Remove dark mode classes for different templates
            document.documentElement.classList.remove('dark-mode');
            document.documentElement.classList.remove('dark');
            document.documentElement.classList.add('light-mode');
            document.body.classList.remove('dark-mode');
            document.body.classList.remove('dark');
            document.body.classList.add('light-mode');
            localStorage.setItem('theme', 'light');
        }

        // Update icon if it exists (for Font Awesome icons)
        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            if (themeIcon.classList.contains('fa-sun') || themeIcon.classList.contains('fa-moon')) {
                // Font Awesome icon toggle
                if (isDark) {
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                } else {
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                }
            } else {
                // Emoji icon toggle (for index.html)
                themeIcon.textContent = isDark ? '☀️' : '🌙';
            }
        }

        // Update theme toggle text if it exists
        const themeText = document.querySelector('.theme-text');
        if (themeText) {
            themeText.textContent = isDark ? 'Light Mode' : 'Dark Mode';
        }

        // Apply theme-specific fixes for Tailwind classes
        this.applyTailwindThemeFixes(isDark);

        // Apply theme-specific fixes for form elements
        this.applyFormThemeFixes(isDark);

        // Apply theme-specific fixes for tables
        this.applyTableThemeFixes(isDark);

        // Dispatch a custom event that templates can listen for
        document.dispatchEvent(new CustomEvent('themeChanged', { detail: { isDark } }));
    },

    /**
     * Apply theme fixes for Tailwind CSS classes
     * @param {boolean} isDark - Whether dark mode is enabled
     */
    applyTailwindThemeFixes: function(isDark) {
        if (isDark) {
            // Update background colors
            document.querySelectorAll('.bg-white').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.backgroundColor = '#1f2937';
                    el.classList.add('theme-fixed');
                }
            });

            document.querySelectorAll('.bg-gray-50, .bg-gray-100').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.backgroundColor = '#374151';
                    el.classList.add('theme-fixed');
                }
            });

            // Update text colors
            document.querySelectorAll('.text-gray-800, .text-gray-900').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#f9fafb';
                    el.classList.add('theme-fixed');
                }
            });

            document.querySelectorAll('.text-gray-700').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#e5e7eb';
                    el.classList.add('theme-fixed');
                }
            });

            document.querySelectorAll('.text-gray-600, .text-gray-500').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#d1d5db';
                    el.classList.add('theme-fixed');
                }
            });

            // Update border colors
            document.querySelectorAll('.border-gray-200, .border-gray-300').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.borderColor = '#4b5563';
                    el.classList.add('theme-fixed');
                }
            });
        } else {
            // Reset to light mode
            document.querySelectorAll('.theme-fixed').forEach(el => {
                el.style.backgroundColor = '';
                el.style.color = '';
                el.style.borderColor = '';
                el.classList.remove('theme-fixed');
            });
        }
    },

    /**
     * Apply theme fixes for form elements
     * @param {boolean} isDark - Whether dark mode is enabled
     */
    applyFormThemeFixes: function(isDark) {
        const formElements = document.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]), select, textarea, .form-control, .form-select');

        formElements.forEach(el => {
            if (isDark) {
                el.style.backgroundColor = '#374151';
                el.style.color = '#f9fafb';
                el.style.borderColor = '#6b7280';
            } else {
                el.style.backgroundColor = '#ffffff';
                el.style.color = '#1a202c';
                el.style.borderColor = '#d1d5db';
            }
        });
    },

    /**
     * Apply theme fixes for tables
     * @param {boolean} isDark - Whether dark mode is enabled
     */
    applyTableThemeFixes: function(isDark) {
        const tables = document.querySelectorAll('table, .table');

        tables.forEach(table => {
            if (isDark) {
                table.style.color = '#f9fafb';

                // Update table headers
                table.querySelectorAll('th').forEach(th => {
                    th.style.backgroundColor = '#374151';
                    th.style.color = '#f9fafb';
                    th.style.borderColor = '#4b5563';
                });

                // Update table cells
                table.querySelectorAll('td').forEach(td => {
                    td.style.color = '#f9fafb';
                    td.style.borderColor = '#4b5563';
                });
            } else {
                table.style.color = '#1a202c';

                // Reset table headers
                table.querySelectorAll('th').forEach(th => {
                    th.style.backgroundColor = '#f9fafb';
                    th.style.color = '#1a202c';
                    th.style.borderColor = '#e5e7eb';
                });

                // Reset table cells
                table.querySelectorAll('td').forEach(td => {
                    td.style.color = '#1a202c';
                    td.style.borderColor = '#e5e7eb';
                });
            }
        });
    },

    /**
     * Initialize dark mode based on user preference
     */
    initDarkMode: function() {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            this.toggleDarkMode(true);
        } else if (savedTheme === 'light') {
            this.toggleDarkMode(false);
        } else {
            // Default to light mode and apply fixes
            this.toggleDarkMode(false);
        }

        // Add theme toggle button if it exists
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                  document.documentElement.classList.contains('dark');
                this.toggleDarkMode(!isDarkMode);
            });
        }

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme')) {
                    this.toggleDarkMode(e.matches);
                }
            });
        }

        // Apply fixes to any dynamically loaded content
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');

                    // Apply theme fixes to newly added content
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.applyThemeToElement(node, isDarkMode);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    /**
     * Apply theme fixes to a specific element
     * @param {Element} element - The element to apply theme fixes to
     * @param {boolean} isDark - Whether dark mode is enabled
     */
    applyThemeToElement: function(element, isDark) {
        // Apply Tailwind fixes to the element and its children
        if (isDark) {
            element.querySelectorAll('.bg-white').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.backgroundColor = '#1f2937';
                    el.classList.add('theme-fixed');
                }
            });

            element.querySelectorAll('.text-gray-800, .text-gray-900').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#f9fafb';
                    el.classList.add('theme-fixed');
                }
            });

            element.querySelectorAll('.text-gray-700').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#e5e7eb';
                    el.classList.add('theme-fixed');
                }
            });

            element.querySelectorAll('.text-gray-600, .text-gray-500').forEach(el => {
                if (!el.classList.contains('theme-fixed')) {
                    el.style.color = '#d1d5db';
                    el.classList.add('theme-fixed');
                }
            });
        }

        // Apply form fixes
        const formElements = element.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]), select, textarea, .form-control, .form-select');
        formElements.forEach(el => {
            if (isDark) {
                el.style.backgroundColor = '#374151';
                el.style.color = '#f9fafb';
                el.style.borderColor = '#6b7280';
            } else {
                el.style.backgroundColor = '#ffffff';
                el.style.color = '#1a202c';
                el.style.borderColor = '#d1d5db';
            }
        });

        // Apply table fixes
        const tables = element.querySelectorAll('table, .table');
        tables.forEach(table => {
            if (isDark) {
                table.style.color = '#f9fafb';
                table.querySelectorAll('th').forEach(th => {
                    th.style.backgroundColor = '#374151';
                    th.style.color = '#f9fafb';
                    th.style.borderColor = '#4b5563';
                });
                table.querySelectorAll('td').forEach(td => {
                    td.style.color = '#f9fafb';
                    td.style.borderColor = '#4b5563';
                });
            } else {
                table.style.color = '#1a202c';
                table.querySelectorAll('th').forEach(th => {
                    th.style.backgroundColor = '#f9fafb';
                    th.style.color = '#1a202c';
                    th.style.borderColor = '#e5e7eb';
                });
                table.querySelectorAll('td').forEach(td => {
                    td.style.color = '#1a202c';
                    td.style.borderColor = '#e5e7eb';
                });
            }
        });
    }
};

// Initialize dark mode when DOM is loaded (only if DMSUtils exists)
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.DMSUtils !== 'undefined') {
        window.DMSUtils.initDarkMode();
    }
});

// Close the IIFE
})();
