import os
import json
import logging
import requests
import secrets
import string
from flask import current_app
from app.services.vector_db import OLLAMA_BASE_URL

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DEFAULT_MODELS_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config', 'default_models.json')

def generate_secure_secret_key(length=32):
    """Generate a secure random secret key."""
    alphabet = string.ascii_letters + string.digits + string.punctuation
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def get_models_data():
    # Get available models from Ollama
    models = []
    embedding_models = []
    vision_models = []

    try:
        # Get models from Ollama
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()

            # Process models
            for model in data.get('models', []):
                model_name = model.get('name', '')
                model_size = model.get('size', 0)
                model_name_lower = model_name.lower()

                # Create model dict
                model_dict = {
                    'name': model_name,
                    'size': model_size
                }

                # Categorize embedding models first (most specific)
                if 'embed' in model_name_lower or model_name in ['bge-m3:latest', 'nomic-embed-text:latest']:
                    embedding_models.append(model_dict.copy())
                    continue

                # Check for vision models - explicit vision models and multimodal models
                is_vision_model = (
                    'vision' in model_name_lower or
                    model_name.endswith('-vision') or
                    # Gemma 3 multimodal models (4b and 12b variants with -it suffix)
                    (model_name_lower.startswith("gemma3:") and
                     any(size in model_name_lower for size in ["4b", "12b"]) and
                     ("-it" in model_name_lower or "instruct" in model_name_lower))
                )

                # Check if it's a general LLM model (not embedding, could be multimodal)
                is_llm_model = not ('embed' in model_name_lower)

                # Add to appropriate categories
                if is_vision_model:
                    vision_models.append(model_dict.copy())

                    # Multimodal models can also be used as regular LLMs
                    if (model_name_lower.startswith("gemma3:") and
                        any(size in model_name_lower for size in ["4b", "12b"]) and
                        ("-it" in model_name_lower or "instruct" in model_name_lower)):
                        models.append(model_dict.copy())
                elif is_llm_model:
                    models.append(model_dict.copy())

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Ollama: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting models from Ollama: {str(e)}")

    # If no models found, add default ones
    if not models:
        models = [
            {"name": "llama3.1:8b-instruct-q4_K_M", "size": 4.9 * 1024 * 1024 * 1024},
            {"name": "mistral:latest", "size": 4.1 * 1024 * 1024 * 1024},
            {"name": "llama3.2:3b-instruct-q4_K_M", "size": 2.0 * 1024 * 1024 * 1024},
            {"name": "gemma3:1b", "size": 815 * 1024 * 1024}
        ]

    if not embedding_models:
        embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]

    if not vision_models:
        vision_models = [
            {"name": "llama3.2-vision:11b-instruct-q4_K_M", "size": 6.8 * 1024 * 1024 * 1024},
            {"name": "gemma3:4b-it-q4_K_M", "size": 3.3 * 1024 * 1024 * 1024},
            {"name": "gemma3:12b-it-q4_K_M", "size": 7.2 * 1024 * 1024 * 1024}
        ]

    # Get current configuration
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get model parameters
    default_llm = defaults.get('llm_model', 'llama3.1:8b-instruct-q4_K_M')
    default_embedding = defaults.get('embedding_model', 'mxbai-embed-large:latest')
    default_vision = defaults.get('vision_model', 'llama3.2-vision:11b-instruct-q4_K_M')
    use_vision = defaults.get('use_vision_model', False)
    use_vision_during_embedding = defaults.get('use_vision_model_during_embedding', False)
    filter_pdf_images = defaults.get('filter_pdf_images', True)
    filter_sensitivity = defaults.get('filter_sensitivity', 'medium')
    max_pdf_images = defaults.get('max_pdf_images', 30)
    show_filtered_images = defaults.get('show_filtered_images', False)
    model_params_all = defaults.get('model_parameters', {})
    # For backward compatibility, also provide the 'balanced' mode as the default
    model_params = model_params_all.get('balanced', {})
    temperature = model_params.get('temperature', 0.7)
    num_ctx = model_params.get('num_ctx', 4096)
    num_predict = model_params.get('num_predict', 256)
    top_p = model_params.get('top_p', 0.9)
    top_k = model_params.get('top_k', 40)
    repeat_penalty = model_params.get('repeat_penalty', 1.1)
    system_prompt = model_params.get('system_prompt', '')

    return {
        'models': models,
        'embeddings': embedding_models,
        'vision_models': vision_models,
        'selected_model': current_app.config.get('SELECTED_MODEL', default_llm),
        'selected_embedding': current_app.config.get('SELECTED_EMBEDDING', default_embedding),
        'selected_vision_model': current_app.config.get('SELECTED_VISION_MODEL', default_vision),
        'use_vision_model': use_vision,
        'use_vision_model_during_embedding': use_vision_during_embedding,
        'filter_pdf_images': filter_pdf_images,
        'filter_sensitivity': filter_sensitivity,
        'max_pdf_images': max_pdf_images,
        'show_filtered_images': show_filtered_images,
        'model_parameters': {
            'strict': model_params_all.get('strict', {}),
            'balanced': model_params_all.get('balanced', {}),
            'off': model_params_all.get('off', {}),
            'default': model_params  # for backward compatibility
        }
    }

def get_query_config_data():
    # Load current configuration from environment variables or defaults
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get query parameters from defaults
    query_params = defaults.get('query_parameters', {})

    # Extract specific parameters with defaults
    preamble = query_params.get('preamble', '')
    anti_hallucination_modes = query_params.get('anti_hallucination_modes', {
        'default_mode': 'strict',
        'custom_instructions': ''
    })
    prompt_templates = query_params.get('prompt_templates', {
        'strict': '',
        'balanced': '',
        'off': '',
        'general': '',
        'document_specific': ''
    })
    insufficient_info_phrases = query_params.get('insufficient_info_phrases', [
        "I don't have enough information",
        "The provided context does not contain",
        "There is no information"
    ])
    followup_question_templates = query_params.get('followup_question_templates', {
        'default': '',
        'insufficient_info': ''
    })

    # Get vision model settings
    use_vision = defaults.get('use_vision_model', False)

    # Get new query configuration parameters with defaults
    retrieval_k = query_params.get('retrieval_k', 12)
    relevance_threshold = query_params.get('relevance_threshold', 0.15)
    min_documents = query_params.get('min_documents', 3)
    max_documents = query_params.get('max_documents', 8)
    max_pdf_images_display = query_params.get('max_pdf_images_display', 5)
    max_url_images_display = query_params.get('max_url_images_display', 5)
    max_tables_display = query_params.get('max_tables_display', 3)
    max_pdf_links_display = query_params.get('max_pdf_links_display', 10)
    max_vision_context_length = query_params.get('max_vision_context_length', 2000)
    context_truncation_strategy = query_params.get('context_truncation_strategy', 'end')

    # Get hallucination detection parameters with defaults (now at root level)
    hallucination_detection = defaults.get('hallucination_detection', {})
    threshold_strict = hallucination_detection.get('threshold_strict', 0.6)
    threshold_balanced = hallucination_detection.get('threshold_balanced', 0.4)
    threshold_default = hallucination_detection.get('threshold_default', 0.5)
    min_statement_length = hallucination_detection.get('min_statement_length', 20)

    return {
        'preamble': preamble,
        'anti_hallucination_modes': anti_hallucination_modes,
        'prompt_templates': prompt_templates,
        'insufficient_info_phrases': insufficient_info_phrases,
        'followup_question_templates': followup_question_templates,
        'use_vision': use_vision,
        'query_parameters': {
            'retrieval_k': retrieval_k,
            'relevance_threshold': relevance_threshold,
            'min_documents': min_documents,
            'max_documents': max_documents,
            'max_pdf_images_display': max_pdf_images_display,
            'max_url_images_display': max_url_images_display,
            'max_tables_display': max_tables_display,
            'max_pdf_links_display': max_pdf_links_display,
            'max_vision_context_length': max_vision_context_length,
            'context_truncation_strategy': context_truncation_strategy
        },
        'hallucination_detection': {
            'threshold_strict': threshold_strict,
            'threshold_balanced': threshold_balanced,
            'threshold_default': threshold_default,
            'min_statement_length': min_statement_length
        }
    }

def get_embedding_config_data():
    # Load current configuration from environment variables or defaults
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get embedding parameters from defaults
    embedding_params = defaults.get('embedding_parameters', {})

    # Set default values if not present
    if not embedding_params:
        embedding_params = {
            "chunk_size": 800,
            "chunk_overlap": 250,
            "extract_tables": True,
            "extract_images": True,
            "extract_locations": False,
            "location_confidence_threshold": 0.5,
            "max_locations_per_document": 50,
            "enable_geocoding": True,
            "use_vision_model": False,
            "filter_sensitivity": "medium",
            "max_images": 30,
            "batch_size": 50,
            "processing_threads": 4
        }

    # Get available embedding models
    available_embedding_models = []
    try:
        # Try to get models from Ollama
        models_response = requests.get(f"{OLLAMA_BASE_URL}/api/tags")
        if models_response.status_code == 200:
            models_data = models_response.json()

            # Filter for embedding models
            embedding_models = []
            for model in models_data.get('models', []):
                model_name = model.get('name', '')
                if 'embed' in model_name.lower() or model_name in ['bge-m3:latest', 'nomic-embed-text:latest']:
                    embedding_models.append({
                        "name": model_name,
                        "size": model.get('size', 0)
                    })

            available_embedding_models = embedding_models
    except Exception as e:
        logger.error(f"Failed to get available models: {str(e)}")

    # If no embedding models found, add default ones
    if not available_embedding_models:
        available_embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]

    # Get vision model settings
    use_vision_during_embedding = defaults.get('use_vision_model_during_embedding', False)
    filter_sensitivity = defaults.get('filter_sensitivity', 'medium')
    max_pdf_images = defaults.get('max_pdf_images', 30)
    show_filtered_images = defaults.get('show_filtered_images', False)

    return {
        'embedding_params': embedding_params,
        'available_embedding_models': available_embedding_models,
        'use_vision_during_embedding': use_vision_during_embedding,
        'filter_sensitivity': filter_sensitivity,
        'max_pdf_images': max_pdf_images,
        'show_filtered_images': show_filtered_images
    }

class Config:
    """Flask configuration settings."""
    # Generate a secure secret key if not provided
    _secret_key = os.getenv("FLASK_SECRET_KEY")
    if not _secret_key or _secret_key == "1qazxsw23edcvfr4":
        _secret_key = generate_secure_secret_key(32)
        logger.info("Generated secure secret key for Flask application")
    
    SECRET_KEY = _secret_key
    MAX_CONTENT_LENGTH = 64 * 1024 * 1024  # 64MB max file size
    WTF_CSRF_ENABLED = os.getenv('WTF_CSRF_ENABLED', 'true').lower() == 'true'
    WTF_CSRF_TIME_LIMIT = int(os.getenv('WTF_CSRF_TIME_LIMIT', '7200'))
    WTF_CSRF_SSL_STRICT = os.getenv('WTF_CSRF_SSL_STRICT', 'false').lower() == 'true'
    WTF_CSRF_CHECK_DEFAULT = os.getenv('WTF_CSRF_CHECK_DEFAULT', 'true').lower() == 'true'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('SESSION_TIMEOUT_MINUTES', '120')) * 60

def validate_csrf_configuration():
    """
    Validate CSRF configuration and return any issues found.
    
    Returns:
        dict: Dictionary with validation results and any issues found
    """
    issues = []
    warnings = []
    
    # Check if CSRF is enabled
    csrf_enabled = os.getenv('WTF_CSRF_ENABLED', 'true').lower() == 'true'
    if not csrf_enabled:
        warnings.append("CSRF protection is disabled. This is not recommended for production.")
    
    # Check secret key
    secret_key = os.getenv("FLASK_SECRET_KEY")
    if not secret_key or secret_key == "1qazxsw23edcvfr4":
        # The application will generate a secure key automatically
        warnings.append("No FLASK_SECRET_KEY environment variable set. A secure key will be generated automatically.")
    else:
        logger.info("Using provided FLASK_SECRET_KEY environment variable")
    
    # Check CSRF time limit
    try:
        csrf_time_limit = int(os.getenv('WTF_CSRF_TIME_LIMIT', '7200'))
        if csrf_time_limit < 3600:  # Less than 1 hour
            warnings.append(f"CSRF time limit is set to {csrf_time_limit} seconds, which may be too short for some users.")
        elif csrf_time_limit > 86400:  # More than 24 hours
            warnings.append(f"CSRF time limit is set to {csrf_time_limit} seconds, which may be too long for security.")
    except ValueError:
        issues.append("Invalid WTF_CSRF_TIME_LIMIT value. Must be a number.")
    
    # Check SSL strict mode
    ssl_strict = os.getenv('WTF_CSRF_SSL_STRICT', 'false').lower() == 'true'
    if ssl_strict:
        warnings.append("CSRF SSL strict mode is enabled. Ensure HTTPS is properly configured.")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'warnings': warnings,
        'csrf_enabled': csrf_enabled,
        'secret_key_configured': secret_key is not None and secret_key != "1qazxsw23edcvfr4",
        'ssl_strict': ssl_strict
    }

def load_default_models():
    """Load the default models configuration from the JSON file."""
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            return json.load(f)
    return {}

def save_default_models(data):
    """Save the default models configuration to the JSON file."""
    with open(DEFAULT_MODELS_FILE, 'w') as f:
        json.dump(data, f, indent=4)

def update_env_file(key, value):
    """Update or add a key-value pair to the .env file."""
    env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
    lines = []
    found = False
    
    if os.path.exists(env_file_path):
        with open(env_file_path, 'r') as f:
            lines = f.readlines()

    with open(env_file_path, 'w') as f:
        for line in lines:
            if line.strip().startswith(f"{key}="):
                f.write(f"{key}={value}\n")
                found = True
            else:
                f.write(line)
        if not found:
            f.write(f"\n{key}={value}\n")

