{"config_version": "1.2.0", "last_updated": "2025-07-29", "llm_model": "llama3.2:3b-instruct-q4_K_M", "embedding_model": "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q6_K", "vision_model": "gemma3:4b-it-q4_K_M", "use_vision_model": false, "filter_pdf_images": true, "filter_sensitivity": "medium", "max_pdf_images": 30, "show_filtered_images": false, "use_vision_model_during_embedding": false, "model_parameters": {"strict": {"temperature": 0.1, "num_ctx": 4096, "num_predict": 256, "top_p": 0.7, "top_k": 1, "repeat_penalty": 1.2}, "balanced": {"temperature": 0.5, "num_ctx": 5120, "num_predict": 384, "top_p": 0.9, "top_k": 40, "repeat_penalty": 1.1}, "off": {"temperature": 1.0, "num_ctx": 5120, "num_predict": 512, "top_p": 0.95, "top_k": 64, "repeat_penalty": 1.0}}, "shared_instructions": {"citations": ["NEVER generate HTML links or <a> tags â€” the system will create these automatically.", "When referencing a document, use ONLY the Citation Filename in square brackets.", "Format examples:", "  â€¢ \"According to [filename.pdf], â€¦\"", "  â€¢ \"As stated in [filename.pdf], â€¦\""], "scientific_names": ["Write scientific names (binomial nomenclature) in plain text; the system will format them in *Genus species*.", "Include subspecies in plain text when present (e.g. Homo sapiens sapiens).", "Keep author citations and years in regular text (e.g. Escherichi<PERSON> coli (Migula 1895)).", "Do not include common names or additional taxonomic ranks."]}, "fallback_responses": {"strict": "Iâ€™m sorry, but there isnâ€™t enough information in the provided context to answer your question. For further assistance, you may contact the Environmental Research and Development Bureau (ERDB) at <EMAIL>.", "balanced": "Iâ€™m sorry, but there isnâ€™t enough information in the provided context to answer your question. You may refine your query or consult ERDB <NAME_EMAIL>.", "off": "Iâ€™m sorry, but I canâ€™t find enough details in the provided context. For further help, contact <NAME_EMAIL>."}, "prompt_templates": {"strict": "ANTIâ€‘HALLUCINATION (STRICT MODE):\n1. Answer ONLY from the provided context.\n2. If information is missing, reply with the strict fallback response.\n3. Cite each fact using the exact filename in [brackets].\n\n**Context:**\n{context}\n\n**Question:**\n{question}\n\n**Answer:**", "balanced": "ANTIâ€‘HALLUCINATION (BALANCED MODE):\n1. Prioritize context, but reasonable inferences are allowed.\n2. Clearly distinguish facts vs. inferences.\n3. If context is insufficient, use the balanced fallback response.\n\n**Context:**\n{context}\n\n**Question:**\n{question}\n\n**Answer:**", "off": "CREATIVE MODE (OFF):\n1. Use context as primary source; supplement with external knowledge sparingly.\n2. Clearly label any added information beyond context.\n3. If context is wholly insufficient, use the off-mode fallback response.\n\n**Context:**\n{context}\n\n**Question:**\n{question}\n\n**Answer:**", "general": "**Context:**\n{context}\n\n**Question:**\n{question}\n\n**Answer:**", "document_specific": "**Document:**\n{context}\n\n**Question:**\n{question}\n\n**Answer:**"}, "query_parameters": {"preamble": "CONTEXT INFORMATION:\n- The following documents have been retrieved based on their relevance to your question.\n- Focus on documents with higher relevance scores.\n- If the context is insufficient, use the modeâ€‘specific fallback response.", "followup_question_count": {"strict": 1, "balanced": 3, "off": 5}, "schema_path": "schema.json", "environment_override": "ERDB_MODE"}, "hallucination_detection": {"threshold_strict": "0.6", "threshold_balanced": "0.4", "threshold_default": "0.5", "min_statement_length": "20", "enable_detection": true}, "embedding_parameters": {"dynamic_chunking": {"strategy": "adaptive", "min_size": 600, "max_size": 1000, "overlap_ratio": 0.2}, "extract_tables": false, "extract_images": false, "enable_location_extraction": false, "enable_page_level_citations": true, "enable_cross_reference": true, "chunk_size": 800, "chunk_overlap": 160, "extract_locations": false, "location_confidence_threshold": 0.5, "max_locations_per_document": 50, "enable_geocoding": false, "batch_size": 10, "processing_threads": 4}, "llamaindex_config": {}}