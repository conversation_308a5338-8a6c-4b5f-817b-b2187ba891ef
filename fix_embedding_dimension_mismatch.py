#!/usr/bin/env python3
"""
Fix embedding dimension mismatch by re-embedding all documents with the new model.
This script handles the transition from 1024-dimensional to 768-dimensional embeddings.
"""

import os
import sys
import logging
import shutil
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_embedding_dimension_mismatch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_chroma_db():
    """Create a backup of the current ChromaDB."""
    try:
        unified_chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        if not os.path.exists(unified_chroma_path):
            logger.error(f"ChromaDB path {unified_chroma_path} does not exist")
            return None
        
        timestamp = int(time.time())
        backup_path = f"{unified_chroma_path}_backup_{timestamp}"
        
        logger.info(f"Creating backup at: {backup_path}")
        shutil.copytree(unified_chroma_path, backup_path)
        logger.info(f"Backup created successfully: {backup_path}")
        
        return backup_path
    except Exception as e:
        logger.error(f"Failed to create backup: {str(e)}")
        return None

def clear_chroma_collections():
    """Clear all collections from ChromaDB."""
    try:
        import chromadb
        from chromadb.config import Settings
        
        unified_chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        
        # Connect to ChromaDB
        client = chromadb.PersistentClient(
            path=unified_chroma_path,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # List all collections
        collections = client.list_collections()
        logger.info(f"Found {len(collections)} collections to clear")
        
        # Delete each collection
        for collection in collections:
            collection_name = collection.name
            logger.info(f"Deleting collection: {collection_name}")
            client.delete_collection(collection_name)
        
        logger.info("All collections cleared successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to clear collections: {str(e)}")
        return False

def test_new_embedding_model():
    """Test the new embedding model to ensure it works."""
    try:
        from langchain_ollama.embeddings import OllamaEmbeddings
        
        embedding_model = os.getenv("TEXT_EMBEDDING_MODEL", "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q6_K")
        logger.info(f"Testing embedding model: {embedding_model}")
        
        # Initialize embedding model
        embed_fn = OllamaEmbeddings(model=embedding_model)
        
        # Test embedding
        test_embedding = embed_fn.embed_query("This is a test")
        dimension = len(test_embedding)
        
        logger.info(f"✅ Embedding model test successful: {dimension} dimensions")
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding model test failed: {str(e)}")
        return False

def main():
    """Main function to fix the embedding dimension mismatch."""
    logger.info("=== Fixing Embedding Dimension Mismatch ===")
    
    # Check current environment
    embedding_model = os.getenv("TEXT_EMBEDDING_MODEL", "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q6_K")
    logger.info(f"Current embedding model: {embedding_model}")
    
    # Test the new embedding model
    if not test_new_embedding_model():
        logger.error("Embedding model test failed. Cannot proceed.")
        return False
    
    # Create backup
    backup_path = backup_chroma_db()
    if not backup_path:
        logger.error("Failed to create backup. Cannot proceed safely.")
        return False
    
    # Clear collections
    if not clear_chroma_collections():
        logger.error("Failed to clear collections.")
        return False
    
    logger.info("✅ Embedding dimension mismatch fixed!")
    logger.info("📝 Next steps:")
    logger.info("1. Restart your application")
    logger.info("2. Re-upload any documents you need")
    logger.info(f"3. Backup available at: {backup_path}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 