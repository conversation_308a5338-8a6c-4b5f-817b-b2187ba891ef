import os
import logging
import chromadb
import json
from langchain_ollama import ChatOllama
from langchain_ollama.embeddings import OllamaEmbeddings

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")

def check_model_dimensions():
    """Check the dimensions of the current embedding model"""
    try:
        # Initialize the embedding model
        embed_fn = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)
        
        # Generate an embedding for a test string
        test_embedding = embed_fn.embed_query("This is a test")
        
        logger.info(f"Current embedding model: {TEXT_EMBEDDING_MODEL}")
        logger.info(f"Embedding dimensions: {len(test_embedding)}")
        
        return len(test_embedding)
    except Exception as e:
        logger.error(f"Error checking model dimensions: {str(e)}")
        return None

def check_collection_dimensions():
    """Check the dimensions of all collections in the Chroma database"""
    try:
        results = {}
        
        # Check unified ChromaDB path
        if not os.path.exists(CHROMA_PATH):
            logger.error(f"Chroma DB path {CHROMA_PATH} does not exist")
            return results
        
        # For unified ChromaDB, we need to check the collections within the database
        try:
            client = chromadb.PersistentClient(path=CHROMA_PATH)
            
            # List all collections
            collections = client.list_collections()
            
            for collection in collections:
                try:
                    collection_name = collection.name
                    
                    # Get collection info
                    collection_info = collection.metadata
                    
                    # Extract dimension information
                    if collection_info and "dimension" in collection_info:
                        dimension = collection_info["dimension"]
                        results[collection_name] = dimension
                        logger.info(f"Collection {collection_name}: dimension = {dimension}")
                    else:
                        # Try to infer from the embeddings
                        try:
                            peek = collection.peek(limit=1)
                            if peek and "embeddings" in peek and peek["embeddings"]:
                                dimension = len(peek["embeddings"][0])
                                results[collection_name] = dimension
                                logger.info(f"Collection {collection_name}: inferred dimension = {dimension}")
                            else:
                                logger.warning(f"Collection {collection_name}: could not determine dimension (empty collection?)")
                                results[collection_name] = "unknown"
                        except Exception as e2:
                            logger.warning(f"Collection {collection_name}: could not determine dimension: {str(e2)}")
                            results[collection_name] = "error"
                except Exception as e:
                    logger.error(f"Error checking dimensions for collection {collection_name}: {str(e)}")
                    results[collection_name] = "error"
                    
        except Exception as e:
            logger.error(f"Error accessing unified ChromaDB: {str(e)}")
            return results
        
        return results
    except Exception as e:
        logger.error(f"Error checking collection dimensions: {str(e)}")
        return {}

def main():
    """Main function to check dimensions"""
    logger.info("Checking embedding dimensions")
    
    # Check current model dimensions
    model_dim = check_model_dimensions()
    if model_dim is None:
        logger.error("Failed to determine current model dimensions")
        return
    
    # Check collection dimensions
    collection_dims = check_collection_dimensions()
    
    # Analyze results
    mismatches = []
    for category, dimension in collection_dims.items():
        if dimension != "unknown" and dimension != "error":
            if int(dimension) != model_dim:
                mismatches.append((category, int(dimension)))
    
    # Print summary
    print("\n=== DIMENSION CHECK SUMMARY ===")
    print(f"Current embedding model: {TEXT_EMBEDDING_MODEL}")
    print(f"Current model dimensions: {model_dim}")
    print(f"\nCollections found: {len(collection_dims)}")
    
    if mismatches:
        print(f"\nDIMENSION MISMATCHES FOUND: {len(mismatches)}/{len(collection_dims)}")
        print("\nThe following collections have dimension mismatches:")
        for category, dim in mismatches:
            print(f"  - {category}: {dim} (expected {model_dim})")
        print("\nRecommendation: Run the reembed_documents.py script to fix these mismatches")
    else:
        print("\nNo dimension mismatches found. All collections match the current embedding model.")

if __name__ == "__main__":
    main()
