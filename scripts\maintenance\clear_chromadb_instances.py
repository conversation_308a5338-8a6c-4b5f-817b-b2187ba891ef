#!/usr/bin/env python3
"""
Script to clear ChromaDB instances and resolve conflicts
"""

import os
import sys
import logging
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "app"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_chromadb_instances():
    """Clear any existing ChromaDB instances that might be causing conflicts."""
    logger.info("Clearing ChromaDB instances to resolve conflicts...")
    
    try:
        # Import the vector_db module
        from services.vector_db import clear_chroma_cache
        
        # Clear the cache
        clear_chroma_cache()
        logger.info("✅ Cleared ChromaDB cache")
        
        # Wait a moment
        time.sleep(2)
        
        # Try to create a new instance to test
        from services.vector_db import get_vector_db
        db = get_vector_db("TEST")
        logger.info("✅ Successfully created new ChromaDB instance")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to clear ChromaDB instances: {str(e)}")
        return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clear ChromaDB instances to resolve conflicts")
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        success = clear_chromadb_instances()
        
        if success:
            logger.info("✅ ChromaDB instance clearing completed successfully")
            sys.exit(0)
        else:
            logger.error("❌ ChromaDB instance clearing failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Script failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 