#!/usr/bin/env python3
"""
Complete System Cleanup Script
Removes all vector data, uploaded files, and resets the system to a clean state.
"""

import os
import shutil
import logging
import sqlite3
from datetime import datetime
from pathlib import Path
import sys

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.config import get_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteCleanup:
    """Complete system cleanup utility."""
    
    def __init__(self, dry_run=False):
        self.dry_run = dry_run
        self.config = get_config()
        self.backup_dir = Path(f"./backups/pre_cleanup_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # Define paths to clean
        self.paths_to_clean = [
            "./data/temp",
            "./data/unified_chroma",
            "./data/chroma",
            "./_temp",
            "./instance"
        ]
        
        # Files to preserve
        self.preserve_files = [
            "chat_history.db",
            "erdb_main.db",
            ".env",
            "config/",
            "logs/",
            "backups/"
        ]
    
    def create_backup(self):
        """Create a backup of current state before cleanup."""
        logger.info("Creating backup of current state...")
        
        if self.dry_run:
            logger.info("DRY RUN: Would create backup at %s", self.backup_dir)
            return True
        
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup temp directories
            temp_backup = self.backup_dir / "temp"
            if os.path.exists("./data/temp"):
                shutil.copytree("./data/temp", temp_backup)
                logger.info("Backed up temp directory")
            
            # Backup unified chroma
            chroma_backup = self.backup_dir / "unified_chroma"
            if os.path.exists("./data/unified_chroma"):
                shutil.copytree("./data/unified_chroma", chroma_backup)
                logger.info("Backed up unified chroma directory")
            
            # Backup legacy chroma
            legacy_chroma_backup = self.backup_dir / "legacy_chroma"
            if os.path.exists("./data/chroma"):
                shutil.copytree("./data/chroma", legacy_chroma_backup)
                logger.info("Backed up legacy chroma directory")
            
            logger.info(f"Backup created at: {self.backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {str(e)}")
            return False
    
    def clean_vector_database(self):
        """Clean the unified vector database."""
        logger.info("Cleaning unified vector database...")
        
        try:
            # Import here to avoid circular imports
            import chromadb
            from chromadb.config import Settings
            
            if not self.dry_run:
                # Use chromadb directly for cleanup
                persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
                client = chromadb.PersistentClient(path=persist_directory)
                collection = client.get_collection(name="unified_collection")
                
                # Get collection stats before cleanup
                count = collection.count()
                logger.info(f"Current database count: {count}")
                
                # Delete all documents
                collection.delete(where={})
                logger.info("Cleared all documents from unified collection")
                
                # Verify cleanup
                new_count = collection.count()
                logger.info(f"Database after cleanup: {new_count} documents")
            else:
                logger.info("DRY RUN: Would clear unified vector database")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clean vector database: {str(e)}")
            return False
    
    def clean_file_system(self):
        """Clean all uploaded files and directories."""
        logger.info("Cleaning file system...")
        
        cleaned_paths = []
        
        for path in self.paths_to_clean:
            if os.path.exists(path):
                if self.dry_run:
                    logger.info(f"DRY RUN: Would remove {path}")
                    cleaned_paths.append(path)
                else:
                    try:
                        if os.path.isdir(path):
                            shutil.rmtree(path)
                            logger.info(f"Removed directory: {path}")
                        else:
                            os.remove(path)
                            logger.info(f"Removed file: {path}")
                        cleaned_paths.append(path)
                    except Exception as e:
                        logger.error(f"Failed to remove {path}: {str(e)}")
            else:
                logger.info(f"Path does not exist: {path}")
        
        # Create fresh temp directory structure
        if not self.dry_run:
            os.makedirs("./data/temp", exist_ok=True)
            os.makedirs("./data/temp/_temp", exist_ok=True)
            logger.info("Created fresh temp directory structure")
        
        return len(cleaned_paths) > 0
    
    def clean_database_entries(self):
        """Clean database entries related to files."""
        logger.info("Cleaning database entries...")
        
        try:
            # Clean main database
            main_db_path = "./erdb_main.db"
            if os.path.exists(main_db_path):
                if self.dry_run:
                    logger.info("DRY RUN: Would clean database entries")
                else:
                    conn = sqlite3.connect(main_db_path)
                    cursor = conn.cursor()
                    
                    # Clean scraped_pages table
                    cursor.execute("DELETE FROM scraped_pages")
                    scraped_deleted = cursor.rowcount
                    logger.info(f"Deleted {scraped_deleted} scraped page entries")
                    
                    # Clean pdf_documents table
                    cursor.execute("DELETE FROM pdf_documents")
                    pdf_deleted = cursor.rowcount
                    logger.info(f"Deleted {pdf_deleted} PDF document entries")
                    
                    # Clean content_sources table
                    cursor.execute("DELETE FROM content_sources")
                    content_deleted = cursor.rowcount
                    logger.info(f"Deleted {content_deleted} content source entries")
                    
                    # Clean vector_embeddings table
                    cursor.execute("DELETE FROM vector_embeddings")
                    vector_deleted = cursor.rowcount
                    logger.info(f"Deleted {vector_deleted} vector embedding entries")
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info("Database entries cleaned successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clean database entries: {str(e)}")
            return False
    
    def verify_cleanup(self):
        """Verify that cleanup was successful."""
        logger.info("Verifying cleanup...")
        
        verification_results = []
        
        # Check vector database
        try:
            import chromadb
            from chromadb.config import Settings
            
            persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
            client = chromadb.PersistentClient(path=persist_directory)
            collection = client.get_collection(name="unified_collection")
            count = collection.count()
            
            if count == 0:
                verification_results.append("✅ Vector database is empty")
            else:
                verification_results.append(f"❌ Vector database still has {count} documents")
        except Exception as e:
            verification_results.append(f"❌ Could not verify vector database: {str(e)}")
        
        # Check file system
        for path in self.paths_to_clean:
            if os.path.exists(path):
                if path == "./data/temp" and os.path.isdir(path):
                    # Check if temp directory is empty or only has _temp subdirectory
                    contents = os.listdir(path)
                    if len(contents) == 0 or (len(contents) == 1 and "_temp" in contents):
                        verification_results.append(f"✅ {path} is clean")
                    else:
                        verification_results.append(f"❌ {path} still has content: {contents}")
                else:
                    verification_results.append(f"❌ {path} still exists")
            else:
                verification_results.append(f"✅ {path} has been removed")
        
        # Check database
        try:
            main_db_path = "./erdb_main.db"
            if os.path.exists(main_db_path):
                conn = sqlite3.connect(main_db_path)
                cursor = conn.cursor()
                
                # Check scraped_pages
                cursor.execute("SELECT COUNT(*) FROM scraped_pages")
                scraped_count = cursor.fetchone()[0]
                if scraped_count == 0:
                    verification_results.append("✅ scraped_pages table is empty")
                else:
                    verification_results.append(f"❌ scraped_pages table has {scraped_count} entries")
                
                # Check pdf_documents
                cursor.execute("SELECT COUNT(*) FROM pdf_documents")
                pdf_count = cursor.fetchone()[0]
                if pdf_count == 0:
                    verification_results.append("✅ pdf_documents table is empty")
                else:
                    verification_results.append(f"❌ pdf_documents table has {pdf_count} entries")
                
                conn.close()
            else:
                verification_results.append("✅ Main database does not exist (clean state)")
                
        except Exception as e:
            verification_results.append(f"❌ Could not verify database: {str(e)}")
        
        # Print verification results
        logger.info("Cleanup verification results:")
        for result in verification_results:
            logger.info(result)
        
        return all("✅" in result for result in verification_results)
    
    def run_cleanup(self):
        """Run the complete cleanup process."""
        logger.info("Starting complete system cleanup...")
        logger.info(f"Dry run mode: {self.dry_run}")
        
        if not self.dry_run:
            # Ask for confirmation
            response = input("This will delete ALL uploaded files and vector data. Are you sure? (yes/no): ")
            if response.lower() != 'yes':
                logger.info("Cleanup cancelled by user")
                return False
        
        # Step 1: Create backup
        if not self.create_backup():
            logger.error("Backup creation failed. Aborting cleanup.")
            return False
        
        # Step 2: Clean vector database
        if not self.clean_vector_database():
            logger.error("Vector database cleanup failed.")
            return False
        
        # Step 3: Clean file system
        if not self.clean_file_system():
            logger.warning("File system cleanup had issues.")
        
        # Step 4: Clean database entries
        if not self.clean_database_entries():
            logger.error("Database cleanup failed.")
            return False
        
        # Step 5: Verify cleanup
        if not self.verify_cleanup():
            logger.warning("Cleanup verification failed. Some items may remain.")
            return False
        
        logger.info("Complete cleanup finished successfully!")
        logger.info(f"Backup available at: {self.backup_dir}")
        
        return True

def main():
    """Main function to run the cleanup."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Complete system cleanup utility")
    parser.add_argument("--dry-run", action="store_true", help="Preview what would be cleaned without actually doing it")
    parser.add_argument("--force", action="store_true", help="Skip confirmation prompt")
    
    args = parser.parse_args()
    
    cleanup = CompleteCleanup(dry_run=args.dry_run)
    
    if args.dry_run:
        logger.info("Running in DRY RUN mode - no actual changes will be made")
    
    success = cleanup.run_cleanup()
    
    if success:
        logger.info("Cleanup completed successfully!")
        sys.exit(0)
    else:
        logger.error("Cleanup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main() 