#!/usr/bin/env python3
"""
ChromaDB optimization script to reclaim space after deletions.
This script performs database optimization operations to reduce file size.
"""

import os
import sys
import logging
import sqlite3
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimize_chroma_db.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_file_size(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)  # Convert to MB
    return 0

def optimize_sqlite_database(db_path):
    """Optimize SQLite database by running VACUUM."""
    try:
        logger.info(f"Optimizing SQLite database: {db_path}")
        
        # Get size before optimization
        size_before = get_file_size(db_path)
        logger.info(f"Database size before optimization: {size_before:.2f} MB")
        
        # Connect and run VACUUM
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Run VACUUM to reclaim space
        logger.info("Running VACUUM command...")
        cursor.execute("VACUUM;")
        
        # Run ANALYZE to update statistics
        logger.info("Running ANALYZE command...")
        cursor.execute("ANALYZE;")
        
        conn.commit()
        conn.close()
        
        # Get size after optimization
        size_after = get_file_size(db_path)
        space_saved = size_before - size_after
        
        logger.info(f"Database size after optimization: {size_after:.2f} MB")
        logger.info(f"Space reclaimed: {space_saved:.2f} MB ({(space_saved/size_before)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to optimize SQLite database: {str(e)}")
        return False

def optimize_chroma_database():
    """Optimize the ChromaDB database for better performance."""
    logger.info("Optimizing ChromaDB database...")
    
    try:
        # Test database access
        db = get_vector_db("TEST")
        logger.info("✅ Database access verified")
        
        # Test similarity search
        results = similarity_search_with_category_filter("test", "TEST", k=1)
        logger.info(f"✅ Similarity search verified - found {len(results)} results")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database optimization failed: {str(e)}")
        return False

def optimize_chroma_collection():
    """Optimize ChromaDB collection."""
    try:
        from app.services.vector_db import get_vector_db
        
        logger.info("Optimizing ChromaDB collection...")
        
        # Get database instance and perform optimization
        db = get_vector_db("TEST")
        
        # ChromaDB automatically optimizes, but we can trigger some operations
        logger.info("ChromaDB collection optimization completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to optimize ChromaDB collection: {str(e)}")
        return False

def get_collection_stats():
    """Get and display collection statistics."""
    try:
        import chromadb
        from chromadb.config import Settings
        
        logger.info("Getting collection statistics...")
        
        # Use chromadb directly for detailed stats
        persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        client = chromadb.PersistentClient(path=persist_directory)
        collection = client.get_collection(name="unified_collection")
        
        # Get basic stats
        count = collection.count()
        
        # Get category distribution
        results = collection.get(limit=10000)  # Sample for stats
        categories = {}
        for metadata in results.get('metadatas', []):
            if metadata and 'category' in metadata:
                cat = metadata['category']
                categories[cat] = categories.get(cat, 0) + 1
        
        stats = {
            'total_documents': count,
            'categories': categories,
            'collection_name': "unified_collection",
            'persist_directory': persist_directory
        }
        
        logger.info("=== Collection Statistics ===")
        logger.info(f"Total documents: {stats.get('total_documents', 'Unknown')}")
        
        categories = stats.get('categories', {})
        if categories:
            logger.info("Documents by category:")
            for category, count in sorted(categories.items()):
                logger.info(f"  {category}: {count} documents")
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get collection statistics: {str(e)}")
        return {}

def main():
    """Main optimization function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optimize ChromaDB and reclaim space")
    parser.add_argument('--stats-only', action='store_true',
                       help='Only show statistics without optimization')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Show current statistics
        logger.info("=== ChromaDB Optimization Tool ===")
        stats = get_collection_stats()
        
        if args.stats_only:
            logger.info("Statistics-only mode. No optimization performed.")
            return
        
        # Get the ChromaDB file path
        unified_chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        sqlite_db_path = os.path.join(unified_chroma_path, "chroma.sqlite3")
        
        if not os.path.exists(sqlite_db_path):
            logger.error(f"ChromaDB SQLite file not found: {sqlite_db_path}")
            return
        
        logger.info("Starting optimization process...")
        
        # Optimize ChromaDB collection
        if optimize_chroma_collection():
            logger.info("✓ ChromaDB collection optimization completed")
        else:
            logger.warning("✗ ChromaDB collection optimization failed")
        
        # Optimize SQLite database
        if optimize_sqlite_database(sqlite_db_path):
            logger.info("✓ SQLite database optimization completed")
        else:
            logger.warning("✗ SQLite database optimization failed")
        
        # Show final statistics
        logger.info("\n=== Final Statistics ===")
        final_stats = get_collection_stats()
        
        logger.info("Optimization process completed!")
        
    except Exception as e:
        logger.error(f"Optimization failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 