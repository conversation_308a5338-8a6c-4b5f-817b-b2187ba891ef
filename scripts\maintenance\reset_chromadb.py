#!/usr/bin/env python3
"""
Script to completely reset ChromaDB and resolve all conflicts
"""

import os
import sys
import shutil
import logging
import time
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_chromadb():
    """Completely reset ChromaDB to resolve conflicts."""
    logger.info("Completely resetting ChromaDB to resolve conflicts...")
    
    try:
        # Get the ChromaDB path
        chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        chroma_path = Path(chroma_path)
        
        if chroma_path.exists():
            # Create backup
            backup_path = chroma_path.parent / f"chroma_backup_{int(time.time())}"
            logger.info(f"Creating backup at: {backup_path}")
            shutil.copytree(chroma_path, backup_path)
            
            # Remove the existing ChromaDB directory
            logger.info(f"Removing existing ChromaDB directory: {chroma_path}")
            shutil.rmtree(chroma_path)
            
            # Wait a moment
            time.sleep(2)
            
            # Recreate the directory
            chroma_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Recreated ChromaDB directory: {chroma_path}")
            
            logger.info("✅ ChromaDB reset completed successfully")
            logger.info(f"Backup created at: {backup_path}")
            return True
        else:
            logger.info("ChromaDB directory does not exist, nothing to reset")
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to reset ChromaDB: {str(e)}")
        return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Reset ChromaDB completely to resolve conflicts")
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--force', '-f', action='store_true',
                       help='Force reset without confirmation')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        if not args.force:
            response = input("This will completely reset ChromaDB and create a backup. Are you sure? (yes/no): ")
            if response.lower() != 'yes':
                logger.info("Reset cancelled by user")
                return
        
        success = reset_chromadb()
        
        if success:
            logger.info("✅ ChromaDB reset completed successfully")
            logger.info("You can now restart your application")
            sys.exit(0)
        else:
            logger.error("❌ ChromaDB reset failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Script failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 