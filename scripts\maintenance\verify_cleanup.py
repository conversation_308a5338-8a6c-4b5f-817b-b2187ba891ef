#!/usr/bin/env python3
"""
Cleanup Verification Script
Verifies that the system cleanup was successful and reports the current state.
"""

import os
import logging
import sqlite3
from pathlib import Path
import sys

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CleanupVerifier:
    """Verify that cleanup was successful."""
    
    def __init__(self):
        self.verification_results = []
        self.issues_found = []
    
    def check_vector_database(self):
        """Check the unified vector database state."""
        logger.info("Checking vector database...")
        
        try:
            from app.services.vector_db import get_vector_db, similarity_search_with_category_filter
            db = get_vector_db("TEST")
            stats = db.get_collection_stats()
            
            total_docs = stats.get('total_documents', 0)
            categories = stats.get('categories', {})
            
            if total_docs == 0:
                self.verification_results.append("✅ Vector database is empty")
                logger.info("Vector database is clean")
            else:
                self.verification_results.append(f"❌ Vector database has {total_docs} documents")
                self.issues_found.append(f"Vector database contains {total_docs} documents")
                logger.warning(f"Vector database contains {total_docs} documents")
            
            if categories:
                self.verification_results.append(f"⚠️  Categories found: {list(categories.keys())}")
                logger.info(f"Categories in database: {list(categories.keys())}")
            else:
                self.verification_results.append("✅ No categories in vector database")
                
        except Exception as e:
            self.verification_results.append(f"❌ Could not check vector database: {str(e)}")
            self.issues_found.append(f"Vector database check failed: {str(e)}")
            logger.error(f"Vector database check failed: {str(e)}")
    
    def check_file_system(self):
        """Check the file system state."""
        logger.info("Checking file system...")
        
        paths_to_check = [
            "./data/temp",
            "./data/unified_chroma",
            "./data/chroma",
            "./_temp"
        ]
        
        for path in paths_to_check:
            if os.path.exists(path):
                if os.path.isdir(path):
                    contents = os.listdir(path)
                    
                    # Special handling for temp directory
                    if path == "./data/temp":
                        if len(contents) == 0:
                            self.verification_results.append(f"✅ {path} is empty")
                        elif len(contents) == 1 and "_temp" in contents:
                            # Check if _temp is empty
                            temp_contents = os.listdir(os.path.join(path, "_temp"))
                            if len(temp_contents) == 0:
                                self.verification_results.append(f"✅ {path} is clean (only empty _temp)")
                            else:
                                self.verification_results.append(f"❌ {path}/_temp contains: {temp_contents}")
                                self.issues_found.append(f"Temp directory contains: {temp_contents}")
                        else:
                            self.verification_results.append(f"❌ {path} contains: {contents}")
                            self.issues_found.append(f"Temp directory contains: {contents}")
                    else:
                        self.verification_results.append(f"❌ {path} still exists with: {contents}")
                        self.issues_found.append(f"Directory {path} still exists")
                else:
                    self.verification_results.append(f"❌ {path} still exists as file")
                    self.issues_found.append(f"File {path} still exists")
            else:
                self.verification_results.append(f"✅ {path} has been removed")
    
    def check_database(self):
        """Check the main database state."""
        logger.info("Checking main database...")
        
        main_db_path = "./erdb_main.db"
        
        if not os.path.exists(main_db_path):
            self.verification_results.append("✅ Main database does not exist (clean state)")
            return
        
        try:
            conn = sqlite3.connect(main_db_path)
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Check scraped_pages
            if 'scraped_pages' in tables:
                cursor.execute("SELECT COUNT(*) FROM scraped_pages")
                scraped_count = cursor.fetchone()[0]
                if scraped_count == 0:
                    self.verification_results.append("✅ scraped_pages table is empty")
                else:
                    self.verification_results.append(f"❌ scraped_pages table has {scraped_count} entries")
                    self.issues_found.append(f"scraped_pages has {scraped_count} entries")
            else:
                self.verification_results.append("✅ scraped_pages table does not exist")
            
            # Check pdf_documents
            if 'pdf_documents' in tables:
                cursor.execute("SELECT COUNT(*) FROM pdf_documents")
                pdf_count = cursor.fetchone()[0]
                if pdf_count == 0:
                    self.verification_results.append("✅ pdf_documents table is empty")
                else:
                    self.verification_results.append(f"❌ pdf_documents table has {pdf_count} entries")
                    self.issues_found.append(f"pdf_documents has {pdf_count} entries")
            else:
                self.verification_results.append("✅ pdf_documents table does not exist")
            
            # Check content_sources
            if 'content_sources' in tables:
                cursor.execute("SELECT COUNT(*) FROM content_sources")
                content_count = cursor.fetchone()[0]
                if content_count == 0:
                    self.verification_results.append("✅ content_sources table is empty")
                else:
                    self.verification_results.append(f"❌ content_sources table has {content_count} entries")
                    self.issues_found.append(f"content_sources has {content_count} entries")
            else:
                self.verification_results.append("✅ content_sources table does not exist")
            
            # Check vector_embeddings
            if 'vector_embeddings' in tables:
                cursor.execute("SELECT COUNT(*) FROM vector_embeddings")
                vector_count = cursor.fetchone()[0]
                if vector_count == 0:
                    self.verification_results.append("✅ vector_embeddings table is empty")
                else:
                    self.verification_results.append(f"❌ vector_embeddings table has {vector_count} entries")
                    self.issues_found.append(f"vector_embeddings has {vector_count} entries")
            else:
                self.verification_results.append("✅ vector_embeddings table does not exist")
            
            conn.close()
            
        except Exception as e:
            self.verification_results.append(f"❌ Could not check database: {str(e)}")
            self.issues_found.append(f"Database check failed: {str(e)}")
            logger.error(f"Database check failed: {str(e)}")
    
    def check_backup(self):
        """Check if backup was created."""
        logger.info("Checking for backup...")
        
        backup_dirs = [d for d in os.listdir("./backups") if d.startswith("pre_cleanup_backup_")]
        
        if backup_dirs:
            latest_backup = max(backup_dirs, key=lambda x: os.path.getctime(os.path.join("./backups", x)))
            self.verification_results.append(f"✅ Backup found: {latest_backup}")
            logger.info(f"Backup available: {latest_backup}")
        else:
            self.verification_results.append("⚠️  No cleanup backup found")
            logger.warning("No cleanup backup found")
    
    def run_verification(self):
        """Run the complete verification process."""
        logger.info("Starting cleanup verification...")
        
        self.check_vector_database()
        self.check_file_system()
        self.check_database()
        self.check_backup()
        
        # Print summary
        logger.info("\n" + "="*50)
        logger.info("CLEANUP VERIFICATION RESULTS")
        logger.info("="*50)
        
        for result in self.verification_results:
            logger.info(result)
        
        logger.info("\n" + "="*50)
        
        if self.issues_found:
            logger.warning("ISSUES FOUND:")
            for issue in self.issues_found:
                logger.warning(f"  - {issue}")
            logger.warning(f"\nTotal issues: {len(self.issues_found)}")
            return False
        else:
            logger.info("✅ All checks passed! System is clean.")
            return True

def main():
    """Main function to run verification."""
    verifier = CleanupVerifier()
    success = verifier.run_verification()
    
    if success:
        print("\n🎉 Cleanup verification successful! System is clean.")
        sys.exit(0)
    else:
        print("\n⚠️  Cleanup verification found issues. Check the log above.")
        sys.exit(1)

if __name__ == "__main__":
    main() 