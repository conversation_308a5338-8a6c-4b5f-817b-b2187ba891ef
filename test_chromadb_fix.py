#!/usr/bin/env python3
"""
Test script to verify ChromaDB conflict resolution.
"""

import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_vector_db_initialization():
    """Test that vector database can be initialized without conflicts."""
    try:
        logger.info("Testing vector database initialization...")
        
        # Import and test the main vector database service
        from app.services.vector_db import get_vector_db, clear_chroma_cache
        
        # Clear any existing instances
        clear_chroma_cache()
        
        # Test initialization for different categories
        categories = ["TEST", "CANOPY", "MANUAL"]
        
        for category in categories:
            logger.info(f"Testing category: {category}")
            db = get_vector_db(category)
            logger.info(f"✓ Successfully initialized vector DB for category: {category}")
        
        logger.info("✓ All vector database tests passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Vector database test failed: {str(e)}")
        return False

def test_llamaindex_fallback():
    """Test that LlamaIndex service handles conflicts gracefully."""
    try:
        logger.info("Testing LlamaIndex service fallback...")
        
        from app.services.llamaindex_vector_db import get_llamaindex_vector_db
        
        # This should either work or gracefully fall back to mock
        llamaindex_db = get_llamaindex_vector_db()
        logger.info("✓ LlamaIndex service initialized (or gracefully disabled)")
        
        # Test if it's a mock object
        try:
            llamaindex_db.similarity_search("test", "TEST", k=1)
            logger.info("✓ LlamaIndex service is functional")
        except NotImplementedError:
            logger.info("✓ LlamaIndex service is gracefully disabled (mock)")
        except Exception as e:
            logger.warning(f"LlamaIndex service error (expected): {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ LlamaIndex test failed: {str(e)}")
        return False

def test_query_service():
    """Test that query service works with the fixed vector database."""
    try:
        logger.info("Testing query service...")
        
        from app.services.query_service import query_category
        
        # Test a simple query
        result = query_category(
            question="test query",
            category="TEST",
            use_llamaindex=False  # Force use of main vector DB
        )
        
        logger.info("✓ Query service test completed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Query service test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("=== ChromaDB Conflict Resolution Test ===")
    
    tests = [
        ("Vector Database Initialization", test_vector_db_initialization),
        ("LlamaIndex Fallback", test_llamaindex_fallback),
        ("Query Service", test_query_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"Test failed: {test_name}")
    
    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("✓ All tests passed! ChromaDB conflict appears to be resolved.")
        return True
    else:
        logger.error("✗ Some tests failed. ChromaDB conflict may still exist.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
