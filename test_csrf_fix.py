#!/usr/bin/env python3
"""
Test script to verify CSRF token functionality
"""

import requests
from bs4 import BeautifulSoup
import json

def test_csrf_token():
    """Test CSRF token generation and extraction"""
    base_url = "http://localhost:8080"
    
    print("Testing CSRF token functionality...")
    
    try:
        # First, get the login page to get a session
        session = requests.Session()
        
        # Get the unified config page (this should require login)
        print("1. Accessing unified config page...")
        response = session.get(f"{base_url}/admin/unified_config")
        
        if response.status_code == 302:
            print("   - Redirected to login (expected)")
            # Follow redirect to login
            login_response = session.get(response.headers['Location'])
            print(f"   - Login page status: {login_response.status_code}")
            
            # Parse login page for CSRF token
            soup = BeautifulSoup(login_response.text, 'html.parser')
            csrf_token = soup.find('input', {'name': 'csrf_token'})
            
            if csrf_token:
                print(f"   - CSRF token found in login form: {csrf_token['value'][:20]}...")
            else:
                print("   - No CSRF token found in login form")
                return False
                
        elif response.status_code == 200:
            print("   - Direct access to unified config page")
            # Parse the page for CSRF tokens
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for meta tag
            meta_csrf = soup.find('meta', {'name': 'csrf-token'})
            if meta_csrf:
                print(f"   - CSRF token found in meta tag: {meta_csrf['content'][:20]}...")
            else:
                print("   - No CSRF token found in meta tag")
            
            # Check for hidden input
            input_csrf = soup.find('input', {'name': 'csrf_token'})
            if input_csrf:
                print(f"   - CSRF token found in hidden input: {input_csrf['value'][:20]}...")
            else:
                print("   - No CSRF token found in hidden input")
            
            # Try to make a POST request with the token
            if meta_csrf:
                token = meta_csrf['content']
                headers = {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': token
                }
                
                test_data = {
                    'tab': 'test',
                    'llm_model': 'test-model'
                }
                
                print("2. Testing POST request with CSRF token...")
                post_response = session.post(
                    f"{base_url}/admin/unified_config",
                    headers=headers,
                    json=test_data
                )
                
                print(f"   - POST response status: {post_response.status_code}")
                if post_response.status_code == 200:
                    print("   - POST request successful with CSRF token")
                    return True
                else:
                    print(f"   - POST request failed: {post_response.text[:200]}")
                    return False
        else:
            print(f"   - Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error testing CSRF token: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_csrf_token()
    if success:
        print("\n✅ CSRF token test passed!")
    else:
        print("\n❌ CSRF token test failed!") 