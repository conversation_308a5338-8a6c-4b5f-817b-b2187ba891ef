#!/usr/bin/env python3
"""
Test script for unified database functionality
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from services.vector_db import get_vector_db, similarity_search_with_category_filter, add_documents_with_category
from langchain.schema import Document
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_unified_database():
    """Test the unified database functionality."""
    print("=== Testing Unified Database ===")
    
    try:
        # Test 1: Initialize database
        print("\n1. Testing database initialization...")
        db = get_vector_db("TEST")
        print("✅ Database initialized successfully")
        
        # Test 2: Add documents with category metadata
        print("\n2. Testing document addition with category metadata...")
        test_docs = [
            Document(
                page_content="This is a test document about agriculture in the Philippines.",
                metadata={"source": "test1.pdf", "page": 1}
            ),
            Document(
                page_content="Research on sustainable farming practices in Southeast Asia.",
                metadata={"source": "test2.pdf", "page": 1}
            )
        ]
        
        add_documents_with_category(test_docs, "TEST")
        print("✅ Documents added successfully")
        
        # Test 3: Search with category filtering
        print("\n3. Testing search with category filtering...")
        results = similarity_search_with_category_filter("agriculture", "TEST", k=5)
        print(f"✅ Found {len(results)} documents for 'agriculture' in TEST category")
        
        for i, doc in enumerate(results):
            print(f"   Document {i+1}: {doc.page_content[:100]}...")
            print(f"   Metadata: {doc.metadata}")
        
        # Test 4: Test different category
        print("\n4. Testing different category...")
        test_docs2 = [
            Document(
                page_content="This is a document about forestry management.",
                metadata={"source": "forest1.pdf", "page": 1}
            )
        ]
        
        add_documents_with_category(test_docs2, "FORESTRY")
        results2 = similarity_search_with_category_filter("forestry", "FORESTRY", k=5)
        print(f"✅ Found {len(results2)} documents for 'forestry' in FORESTRY category")
        
        # Test 5: Verify category separation
        print("\n5. Testing category separation...")
        all_results = db.similarity_search("management", k=10)
        print(f"✅ Found {len(all_results)} total documents for 'management' across all categories")
        
        categories_found = set()
        for doc in all_results:
            if doc.metadata.get('category'):
                categories_found.add(doc.metadata['category'])
        
        print(f"   Categories found: {categories_found}")
        
        print("\n🎉 All tests passed! Unified database is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        logger.error(f"Test error: {str(e)}")
        return False

def test_database_stats():
    """Test database statistics."""
    print("\n=== Testing Database Statistics ===")
    
    try:
        from services.vector_db import get_vector_db, similarity_search_with_category_filter
        
        db = get_vector_db("TEST")
        stats = db.get_collection_stats()
        
        print(f"Database Statistics:")
        print(f"  Total documents: {stats.get('total_documents', 0)}")
        print(f"  Categories: {stats.get('categories', {})}")
        print(f"  Collection name: {stats.get('collection_name', 'N/A')}")
        print(f"  Persist directory: {stats.get('persist_directory', 'N/A')}")
        
        print("✅ Database statistics retrieved successfully")
        return True
        
    except Exception as e:
        print(f"❌ Statistics test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🔍 Unified Database Test Suite")
    print("=" * 50)
    
    # Test basic functionality
    success1 = test_unified_database()
    
    # Test statistics
    success2 = test_database_stats()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The unified database migration is successful.")
        print("\nNext steps:")
        print("1. The unified database is ready for use")
        print("2. All existing functionality will work with improved performance")
        print("3. You can now remove old ChromaDB directories if desired")
    else:
        print("\n❌ Some tests failed. Please check the logs for details.")

if __name__ == "__main__":
    main() 